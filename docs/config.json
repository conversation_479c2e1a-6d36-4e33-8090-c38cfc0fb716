{"$schema": "https://raw.githubusercontent.com/TanStack/tanstack.com/main/tanstack-docs-config.schema.json", "docSearch": {"appId": "", "apiKey": "", "indexName": "tanstack-db"}, "sections": [{"label": "Getting Started", "children": [{"label": "Overview", "to": "overview"}, {"label": "Quick Start", "to": "quick-start"}, {"label": "Installation", "to": "installation"}], "frameworks": [{"label": "react", "children": [{"label": "React Adapter", "to": "framework/react/adapter"}]}, {"label": "solid", "children": [{"label": "Solid Adapter", "to": "framework/solid/adapter"}]}, {"label": "vue", "children": [{"label": "<PERSON><PERSON> Adapter", "to": "framework/vue/adapter"}]}, {"label": "svelte", "children": [{"label": "Svelte Adapter", "to": "framework/svelte/adapter"}]}]}, {"label": "Guides", "children": [{"label": "Live Queries", "to": "guides/live-queries"}, {"label": "Erro<PERSON>", "to": "guides/error-handling"}, {"label": "Creating Collection Options Creators", "to": "guides/collection-options-creator"}]}, {"label": "Collections", "children": [{"label": "Query Collection", "to": "collections/query-collection"}]}, {"label": "API Reference", "children": [{"label": "Core API Reference", "to": "reference/index"}, {"label": "Collection", "to": "reference/interfaces/collection"}, {"label": "createCollection", "to": "reference/functions/createcollection"}, {"label": "liveQueryCollectionOptions", "to": "reference/functions/livequerycollectionoptions"}, {"label": "createLiveQueryCollection", "to": "reference/functions/createlivequerycollection"}, {"label": "createOptimisticAction", "to": "reference/functions/createoptimisticaction"}, {"label": "createTransaction", "to": "reference/functions/createtransaction"}, {"label": "Electric DB Collection", "to": "reference/electric-db-collection/index"}, {"label": "electricCollectionOptions", "to": "reference/electric-db-collection/functions/electriccollectionoptions"}, {"label": "Query DB Collection", "to": "reference/query-db-collection/index"}, {"label": "queryCollectionOptions", "to": "reference/query-db-collection/functions/querycollectionoptions"}], "frameworks": [{"label": "react", "children": [{"label": "React Hooks", "to": "framework/react/reference/index"}, {"label": "useLiveQuery", "to": "framework/react/reference/functions/uselivequery"}]}, {"label": "solid", "children": [{"label": "<PERSON> Hooks", "to": "framework/solid/reference/index"}, {"label": "useLiveQuery", "to": "framework/solid/reference/functions/uselivequery"}]}, {"label": "vue", "children": [{"label": "Vue Composables", "to": "framework/vue/reference/index"}, {"label": "useLiveQuery", "to": "framework/vue/reference/functions/uselivequery"}, {"label": "UseLiveQueryReturn", "to": "framework/vue/reference/interfaces/uselivequeryreturn"}, {"label": "UseLiveQueryReturnWithCollection", "to": "framework/vue/reference/interfaces/uselivequeryreturnwithcollection"}]}]}]}