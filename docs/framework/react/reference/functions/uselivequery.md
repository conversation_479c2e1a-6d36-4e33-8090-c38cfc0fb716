---
id: useLiveQuery
title: useLiveQuery
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: useLiveQuery()

## Call Signature

```ts
function useLiveQuery<TContext>(queryFn, deps?): object
```

Defined in: [useLiveQuery.ts:64](https://github.com/TanStack/db/blob/main/packages/react-db/src/useLiveQuery.ts#L64)

Create a live query using a query function

### Type Parameters

• **TContext** *extends* `Context`

### Parameters

#### queryFn

(`q`) => `QueryBuilder`\<`TContext`\>

Query function that defines what data to fetch

#### deps?

`unknown`[]

Array of dependencies that trigger query re-execution when changed

### Returns

`object`

Object with reactive data, state, and status information

#### collection

```ts
collection: Collection<{ [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }, string | number, {}>;
```

#### data

```ts
data: { [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }[];
```

#### isCleanedUp

```ts
isCleanedUp: boolean;
```

#### isError

```ts
isError: boolean;
```

#### isIdle

```ts
isIdle: boolean;
```

#### isLoading

```ts
isLoading: boolean;
```

#### isReady

```ts
isReady: boolean;
```

#### state

```ts
state: Map<string | number, { [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }>;
```

#### status

```ts
status: CollectionStatus;
```

### Examples

```ts
// Basic query with object syntax
const { data, isLoading } = useLiveQuery((q) =>
  q.from({ todos: todosCollection })
   .where(({ todos }) => eq(todos.completed, false))
   .select(({ todos }) => ({ id: todos.id, text: todos.text }))
)
```

```ts
// With dependencies that trigger re-execution
const { data, state } = useLiveQuery(
  (q) => q.from({ todos: todosCollection })
         .where(({ todos }) => gt(todos.priority, minPriority)),
  [minPriority] // Re-run when minPriority changes
)
```

```ts
// Join pattern
const { data } = useLiveQuery((q) =>
  q.from({ issues: issueCollection })
   .join({ persons: personCollection }, ({ issues, persons }) =>
     eq(issues.userId, persons.id)
   )
   .select(({ issues, persons }) => ({
     id: issues.id,
     title: issues.title,
     userName: persons.name
   }))
)
```

```ts
// Handle loading and error states
const { data, isLoading, isError, status } = useLiveQuery((q) =>
  q.from({ todos: todoCollection })
)

if (isLoading) return <div>Loading...</div>
if (isError) return <div>Error: {status}</div>

return (
  <ul>
    {data.map(todo => <li key={todo.id}>{todo.text}</li>)}
  </ul>
)
```

## Call Signature

```ts
function useLiveQuery<TContext>(config, deps?): object
```

Defined in: [useLiveQuery.ts:113](https://github.com/TanStack/db/blob/main/packages/react-db/src/useLiveQuery.ts#L113)

Create a live query using configuration object

### Type Parameters

• **TContext** *extends* `Context`

### Parameters

#### config

`LiveQueryCollectionConfig`\<`TContext`\>

Configuration object with query and options

#### deps?

`unknown`[]

Array of dependencies that trigger query re-execution when changed

### Returns

`object`

Object with reactive data, state, and status information

#### collection

```ts
collection: Collection<{ [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }, string | number, {}>;
```

#### data

```ts
data: { [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }[];
```

#### isCleanedUp

```ts
isCleanedUp: boolean;
```

#### isError

```ts
isError: boolean;
```

#### isIdle

```ts
isIdle: boolean;
```

#### isLoading

```ts
isLoading: boolean;
```

#### isReady

```ts
isReady: boolean;
```

#### state

```ts
state: Map<string | number, { [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }>;
```

#### status

```ts
status: CollectionStatus;
```

### Examples

```ts
// Basic config object usage
const { data, status } = useLiveQuery({
  query: (q) => q.from({ todos: todosCollection }),
  gcTime: 60000
})
```

```ts
// With query builder and options
const queryBuilder = new Query()
  .from({ persons: collection })
  .where(({ persons }) => gt(persons.age, 30))
  .select(({ persons }) => ({ id: persons.id, name: persons.name }))

const { data, isReady } = useLiveQuery({ query: queryBuilder })
```

```ts
// Handle all states uniformly
const { data, isLoading, isReady, isError } = useLiveQuery({
  query: (q) => q.from({ items: itemCollection })
})

if (isLoading) return <div>Loading...</div>
if (isError) return <div>Something went wrong</div>
if (!isReady) return <div>Preparing...</div>

return <div>{data.length} items loaded</div>
```

## Call Signature

```ts
function useLiveQuery<TResult, TKey, TUtils>(liveQueryCollection): object
```

Defined in: [useLiveQuery.ts:158](https://github.com/TanStack/db/blob/main/packages/react-db/src/useLiveQuery.ts#L158)

Subscribe to an existing live query collection

### Type Parameters

• **TResult** *extends* `object`

• **TKey** *extends* `string` \| `number`

• **TUtils** *extends* `Record`\<`string`, `any`\>

### Parameters

#### liveQueryCollection

`Collection`\<`TResult`, `TKey`, `TUtils`\>

Pre-created live query collection to subscribe to

### Returns

`object`

Object with reactive data, state, and status information

#### collection

```ts
collection: Collection<TResult, TKey, TUtils>;
```

#### data

```ts
data: TResult[];
```

#### isCleanedUp

```ts
isCleanedUp: boolean;
```

#### isError

```ts
isError: boolean;
```

#### isIdle

```ts
isIdle: boolean;
```

#### isLoading

```ts
isLoading: boolean;
```

#### isReady

```ts
isReady: boolean;
```

#### state

```ts
state: Map<TKey, TResult>;
```

#### status

```ts
status: CollectionStatus;
```

### Examples

```ts
// Using pre-created live query collection
const myLiveQuery = createLiveQueryCollection((q) =>
  q.from({ todos: todosCollection }).where(({ todos }) => eq(todos.active, true))
)
const { data, collection } = useLiveQuery(myLiveQuery)
```

```ts
// Access collection methods directly
const { data, collection, isReady } = useLiveQuery(existingCollection)

// Use collection for mutations
const handleToggle = (id) => {
  collection.update(id, draft => { draft.completed = !draft.completed })
}
```

```ts
// Handle states consistently
const { data, isLoading, isError } = useLiveQuery(sharedCollection)

if (isLoading) return <div>Loading...</div>
if (isError) return <div>Error loading data</div>

return <div>{data.map(item => <Item key={item.id} {...item} />)}</div>
```
