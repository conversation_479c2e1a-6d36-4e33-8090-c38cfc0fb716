---
id: useLiveQuery
title: useLiveQuery
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: useLiveQuery()

## Call Signature

```ts
function useLiveQuery<TContext>(queryFn, deps?): UseLiveQueryReturn<{ [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }>
```

Defined in: [useLiveQuery.ts:113](https://github.com/TanStack/db/blob/main/packages/vue-db/src/useLiveQuery.ts#L113)

Create a live query using a query function

### Type Parameters

• **TContext** *extends* `Context`

### Parameters

#### queryFn

(`q`) => `QueryBuilder`\<`TContext`\>

Query function that defines what data to fetch

#### deps?

`unknown`[]

Array of reactive dependencies that trigger query re-execution when changed

### Returns

[`UseLiveQueryReturn`](../../interfaces/uselivequeryreturn.md)\<\{ \[K in string \| number \| symbol\]: (TContext\["result"\] extends object ? any\[any\] : TContext\["hasJoins"\] extends true ? TContext\["schema"\] : TContext\["schema"\]\[TContext\["fromSourceName"\]\])\[K\] \}\>

Reactive object with query data, state, and status information

### Examples

```ts
// Basic query with object syntax
const { data, isLoading } = useLiveQuery((q) =>
  q.from({ todos: todosCollection })
   .where(({ todos }) => eq(todos.completed, false))
   .select(({ todos }) => ({ id: todos.id, text: todos.text }))
)
```

```ts
// With reactive dependencies
const minPriority = ref(5)
const { data, state } = useLiveQuery(
  (q) => q.from({ todos: todosCollection })
         .where(({ todos }) => gt(todos.priority, minPriority.value)),
  [minPriority] // Re-run when minPriority changes
)
```

```ts
// Join pattern
const { data } = useLiveQuery((q) =>
  q.from({ issues: issueCollection })
   .join({ persons: personCollection }, ({ issues, persons }) =>
     eq(issues.userId, persons.id)
   )
   .select(({ issues, persons }) => ({
     id: issues.id,
     title: issues.title,
     userName: persons.name
   }))
)
```

```ts
// Handle loading and error states in template
const { data, isLoading, isError, status } = useLiveQuery((q) =>
  q.from({ todos: todoCollection })
)

// In template:
// <div v-if="isLoading">Loading...</div>
// <div v-else-if="isError">Error: {{ status }}</div>
// <ul v-else>
//   <li v-for="todo in data" :key="todo.id">{{ todo.text }}</li>
// </ul>
```

## Call Signature

```ts
function useLiveQuery<TContext>(config, deps?): UseLiveQueryReturn<{ [K in string | number | symbol]: (TContext["result"] extends object ? any[any] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]])[K] }>
```

Defined in: [useLiveQuery.ts:151](https://github.com/TanStack/db/blob/main/packages/vue-db/src/useLiveQuery.ts#L151)

Create a live query using configuration object

### Type Parameters

• **TContext** *extends* `Context`

### Parameters

#### config

`LiveQueryCollectionConfig`\<`TContext`\>

Configuration object with query and options

#### deps?

`unknown`[]

Array of reactive dependencies that trigger query re-execution when changed

### Returns

[`UseLiveQueryReturn`](../../interfaces/uselivequeryreturn.md)\<\{ \[K in string \| number \| symbol\]: (TContext\["result"\] extends object ? any\[any\] : TContext\["hasJoins"\] extends true ? TContext\["schema"\] : TContext\["schema"\]\[TContext\["fromSourceName"\]\])\[K\] \}\>

Reactive object with query data, state, and status information

### Examples

```ts
// Basic config object usage
const { data, status } = useLiveQuery({
  query: (q) => q.from({ todos: todosCollection }),
  gcTime: 60000
})
```

```ts
// With reactive dependencies
const filter = ref('active')
const { data, isReady } = useLiveQuery({
  query: (q) => q.from({ todos: todosCollection })
                 .where(({ todos }) => eq(todos.status, filter.value))
}, [filter])
```

```ts
// Handle all states uniformly
const { data, isLoading, isReady, isError } = useLiveQuery({
  query: (q) => q.from({ items: itemCollection })
})

// In template:
// <div v-if="isLoading">Loading...</div>
// <div v-else-if="isError">Something went wrong</div>
// <div v-else-if="!isReady">Preparing...</div>
// <div v-else>{{ data.length }} items loaded</div>
```

## Call Signature

```ts
function useLiveQuery<TResult, TKey, TUtils>(liveQueryCollection): UseLiveQueryReturnWithCollection<TResult, TKey, TUtils>
```

Defined in: [useLiveQuery.ts:196](https://github.com/TanStack/db/blob/main/packages/vue-db/src/useLiveQuery.ts#L196)

Subscribe to an existing query collection (can be reactive)

### Type Parameters

• **TResult** *extends* `object`

• **TKey** *extends* `string` \| `number`

• **TUtils** *extends* `Record`\<`string`, `any`\>

### Parameters

#### liveQueryCollection

`MaybeRefOrGetter`\<`Collection`\<`TResult`, `TKey`, `TUtils`, `StandardSchemaV1`\<`unknown`, `unknown`\>, `TResult`\>\>

Pre-created query collection to subscribe to (can be a ref)

### Returns

[`UseLiveQueryReturnWithCollection`](../../interfaces/uselivequeryreturnwithcollection.md)\<`TResult`, `TKey`, `TUtils`\>

Reactive object with query data, state, and status information

### Examples

```ts
// Using pre-created query collection
const myLiveQuery = createLiveQueryCollection((q) =>
  q.from({ todos: todosCollection }).where(({ todos }) => eq(todos.active, true))
)
const { data, collection } = useLiveQuery(myLiveQuery)
```

```ts
// Reactive query collection reference
const selectedQuery = ref(todosQuery)
const { data, collection } = useLiveQuery(selectedQuery)

// Switch queries reactively
selectedQuery.value = archiveQuery
```

```ts
// Access query collection methods directly
const { data, collection, isReady } = useLiveQuery(existingQuery)

// Use underlying collection for mutations
const handleToggle = (id) => {
  collection.value.update(id, draft => { draft.completed = !draft.completed })
}
```

```ts
// Handle states consistently
const { data, isLoading, isError } = useLiveQuery(sharedQuery)

// In template:
// <div v-if="isLoading">Loading...</div>
// <div v-else-if="isError">Error loading data</div>
// <div v-else>
//   <Item v-for="item in data" :key="item.id" v-bind="item" />
// </div>
```
