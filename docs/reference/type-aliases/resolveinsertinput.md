---
id: ResolveInsertInput
title: ResolveInsertInput
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: ResolveInsertInput\<TExplicit, TSchema, TFallback\>

```ts
type ResolveInsertInput<TExplicit, TSchema, TFallback> = unknown extends TExplicit ? [TSchema] extends [never] ? TFallback : InferSchemaInput<TSchema> : TExplicit extends object ? TExplicit : Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:42](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L42)

**`Internal`**

Helper type to determine the insert input type
This takes the raw generics (TExplicit, TSchema, TFallback) instead of the resolved T.

Priority:
1. Explicit generic TExplicit (if not 'unknown')
2. Schema input type (if schema provided)
3. Fallback type TFallback

 This is used for collection insert type inference

## Type Parameters

• **TExplicit** = `unknown`

• **TSchema** *extends* `StandardSchemaV1` = `never`

• **TFallback** *extends* `object` = `Record`\<`string`, `unknown`\>
