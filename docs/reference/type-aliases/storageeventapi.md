---
id: StorageEventApi
title: StorageEventApi
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: StorageEventApi

```ts
type StorageEventApi = object;
```

Defined in: [packages/db/src/local-storage.ts:28](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L28)

Storage event API - subset of Window for 'storage' events only

## Type declaration

### addEventListener()

```ts
addEventListener: (type, listener) => void;
```

#### Parameters

##### type

`"storage"`

##### listener

(`event`) => `void`

#### Returns

`void`

### removeEventListener()

```ts
removeEventListener: (type, listener) => void;
```

#### Parameters

##### type

`"storage"`

##### listener

(`event`) => `void`

#### Returns

`void`
