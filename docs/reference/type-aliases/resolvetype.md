---
id: ResolveType
title: ResolveType
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: ResolveType\<TExplicit, TSchema, TFallback\>

```ts
type ResolveType<TExplicit, TSchema, TFallback> = unknown extends TExplicit ? [TSchema] extends [never] ? TFallback : InferSchemaOutput<TSchema> : TExplicit extends object ? TExplicit : Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:64](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L64)

Helper type to determine the final type based on priority:
1. Explicit generic TExplicit (if not 'unknown')
2. Schema output type (if schema provided)
3. Fallback type TFallback

## Type Parameters

• **TExplicit**

• **TSchema** *extends* `StandardSchemaV1` = `never`

• **TFallback** *extends* `object` = `Record`\<`string`, `unknown`\>

## Remarks

This type is used internally to resolve the collection item type based on the provided generics and schema.
Users should not need to use this type directly, but understanding the priority order helps when defining collections.
