---
id: InferSchemaInput
title: InferSchemaInput
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: InferSchemaInput\<T\>

```ts
type InferSchemaInput<T> = T extends StandardSchemaV1 ? StandardSchemaV1.InferInput<T> extends object ? StandardSchemaV1.InferInput<T> : Record<string, unknown> : Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:25](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L25)

**`Internal`**

Helper type to extract the input type from a standard schema

 This is used for collection insert type inference

## Type Parameters

• **T**
