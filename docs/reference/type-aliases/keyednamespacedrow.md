---
id: KeyedNamespacedRow
title: KeyedNamespacedRow
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: KeyedNamespacedRow

```ts
type KeyedNamespacedRow = [unknown, NamespacedRow];
```

Defined in: [packages/db/src/types.ts:561](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L561)

A keyed namespaced row is a row with a key and a namespaced row
This is the main representation of a row in a query pipeline
