---
id: InsertMutationFn
title: InsertMutationFn
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: InsertMutationFn()\<T, T<PERSON><PERSON>, TUtils\>

```ts
type InsertMutationFn<T, T<PERSON><PERSON>, TUtils> = (params) => Promise<any>;
```

Defined in: [packages/db/src/types.ts:302](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L302)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TUtils** *extends* [`UtilsRecord`](../utilsrecord.md) = `Record`\<`string`, [`Fn`](../fn.md)\>

## Parameters

### params

[`InsertMutationFnParams`](../insertmutationfnparams.md)\<`T`, `TK<PERSON>`, `TUtils`\>

## Returns

`Promise`\<`any`\>
