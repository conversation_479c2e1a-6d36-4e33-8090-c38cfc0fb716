---
id: NamespacedRow
title: NamespacedRow
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: NamespacedRow

```ts
type NamespacedRow = Record<string, Record<string, unknown>>;
```

Defined in: [packages/db/src/types.ts:555](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L555)

A namespaced row is a row withing a pipeline that had each table wrapped in its alias
