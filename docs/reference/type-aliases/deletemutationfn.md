---
id: DeleteMutationFn
title: DeleteMutationFn
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: DeleteMutationFn()\<T, T<PERSON><PERSON>, TUtils\>

```ts
type DeleteMutationFn<T, T<PERSON><PERSON>, TUtils> = (params) => Promise<any>;
```

Defined in: [packages/db/src/types.ts:314](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L314)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TUtils** *extends* [`UtilsRecord`](../utilsrecord.md) = `Record`\<`string`, [`Fn`](../fn.md)\>

## Parameters

### params

[`DeleteMutationFnParams`](../deletemutationfnparams.md)\<`T`, `TKey`, `TUtils`\>

## Returns

`Promise`\<`any`\>
