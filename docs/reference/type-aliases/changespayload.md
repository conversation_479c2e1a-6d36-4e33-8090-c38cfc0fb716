---
id: ChangesPayload
title: ChangesPayload
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: ChangesPayload\<T\>

```ts
type ChangesPayload<T> = ChangeMessage<T>[];
```

Defined in: [packages/db/src/types.ts:531](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L531)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>
