---
id: MutationFn
title: MutationFn
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: MutationFn()\<T\>

```ts
type MutationFn<T> = (params) => Promise<any>;
```

Defined in: [packages/db/src/types.ts:141](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L141)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Parameters

### params

[`MutationFnParams`](../mutationfnparams.md)\<`T`\>

## Returns

`Promise`\<`any`\>
