---
id: IndexResolver
title: IndexResolver
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: IndexResolver\<TKey\>

```ts
type IndexResolver<TKey> = 
  | IndexConstructor<TKey>
| () => Promise<IndexConstructor<TKey>>;
```

Defined in: [packages/db/src/indexes/base-index.ts:117](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L117)

Index resolver can be either a class constructor or async loader

## Type Parameters

• **TKey** *extends* `string` \| `number` = `string` \| `number`
