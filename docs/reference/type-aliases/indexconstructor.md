---
id: IndexConstructor
title: IndexConstructor
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: IndexConstructor()\<TKey\>

```ts
type IndexConstructor<TKey> = (id, expression, name?, options?) => BaseIndex<TKey>;
```

Defined in: [packages/db/src/indexes/base-index.ts:106](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L106)

Type for index constructor

## Type Parameters

• **TKey** *extends* `string` \| `number` = `string` \| `number`

## Parameters

### id

`number`

### expression

`BasicExpression`

### name?

`string`

### options?

`any`

## Returns

[`BaseIndex`](../../classes/baseindex.md)\<`TKey`\>
