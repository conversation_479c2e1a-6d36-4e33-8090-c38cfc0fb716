---
id: DeleteMutationFnParams
title: DeleteMutationFnParams
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: DeleteMutationFnParams\<T, T<PERSON><PERSON>, TUtils\>

```ts
type DeleteMutationFnParams<T, T<PERSON><PERSON>, TUtils> = object;
```

Defined in: [packages/db/src/types.ts:293](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L293)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TUtils** *extends* [`UtilsRecord`](../utilsrecord.md) = `Record`\<`string`, [`Fn`](../fn.md)\>

## Type declaration

### collection

```ts
collection: Collection<T, T<PERSON><PERSON>, TUtils>;
```

### transaction

```ts
transaction: TransactionWithMutations<T, "delete">;
```
