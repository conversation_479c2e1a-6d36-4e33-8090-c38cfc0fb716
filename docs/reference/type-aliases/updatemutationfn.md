---
id: UpdateMutationFn
title: UpdateMutationFn
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: UpdateMutationFn()\<T, T<PERSON><PERSON>, TUtils\>

```ts
type UpdateMutationFn<T, T<PERSON><PERSON>, TUtils> = (params) => Promise<any>;
```

Defined in: [packages/db/src/types.ts:308](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L308)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TUtils** *extends* [`UtilsRecord`](../utilsrecord.md) = `Record`\<`string`, [`Fn`](../fn.md)\>

## Parameters

### params

[`UpdateMutationFnParams`](../updatemutationfnparams.md)\<`T`, `TKey`, `TUtils`\>

## Returns

`Promise`\<`any`\>
