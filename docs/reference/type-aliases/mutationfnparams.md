---
id: MutationFnParams
title: MutationFnParams
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: MutationFnParams\<T\>

```ts
type MutationFnParams<T> = object;
```

Defined in: [packages/db/src/types.ts:137](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L137)

Configuration options for creating a new transaction

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Type declaration

### transaction

```ts
transaction: TransactionWithMutations<T>;
```
