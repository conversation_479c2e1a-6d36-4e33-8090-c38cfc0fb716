---
id: InsertMutationFnParams
title: InsertMutationFnParams
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: InsertMutationFnParams\<T, T<PERSON><PERSON>, TUtils\>

```ts
type InsertMutationFnParams<T, TK<PERSON>, TUtils> = object;
```

Defined in: [packages/db/src/types.ts:285](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L285)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TUtils** *extends* [`UtilsRecord`](../utilsrecord.md) = `Record`\<`string`, [`Fn`](../fn.md)\>

## Type declaration

### collection

```ts
collection: Collection<T, TK<PERSON>, TUtils>;
```

### transaction

```ts
transaction: TransactionWithMutations<T, "insert">;
```
