---
id: KeyedStream
title: KeyedStream
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: KeyedStream

```ts
type KeyedStream = IStreamBuilder<InputRow>;
```

Defined in: [packages/db/src/types.ts:544](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L544)

A keyed stream is a stream of rows
This is used as the inputs from a collection to a query
