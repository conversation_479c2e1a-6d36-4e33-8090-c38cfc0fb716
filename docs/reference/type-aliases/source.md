---
id: Source
title: Source
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: Source

```ts
type Source = object;
```

Defined in: [packages/db/src/query/builder/types.ts:25](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L25)

## Index Signature

```ts
[alias: string]: 
  | CollectionImpl<any, any, {}, StandardSchemaV1<unknown, unknown>, any>
| QueryBuilder<Context>
```
