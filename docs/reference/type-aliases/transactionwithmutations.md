---
id: TransactionWithMutations
title: TransactionWithMutations
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: TransactionWithMutations\<T, TOperation\>

```ts
type TransactionWithMutations<T, TOperation> = Transaction<T> & object;
```

Defined in: [packages/db/src/types.ts:154](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L154)

Utility type for a Transaction with at least one mutation
This is used internally by the Transaction.commit method

## Type declaration

### mutations

```ts
mutations: NonEmptyArray<PendingMutation<T, TOperation>>;
```

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TOperation** *extends* [`OperationType`](../operationtype.md) = [`OperationType`](../operationtype.md)
