---
id: UpdateMutationFnParams
title: UpdateMutationFnParams
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: UpdateMutationFnParams\<T, T<PERSON><PERSON>, TUtils\>

```ts
type UpdateMutationFnParams<T, T<PERSON><PERSON>, TUtils> = object;
```

Defined in: [packages/db/src/types.ts:276](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L276)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TUtils** *extends* [`UtilsRecord`](../utilsrecord.md) = `Record`\<`string`, [`Fn`](../fn.md)\>

## Type declaration

### collection

```ts
collection: Collection<T, T<PERSON><PERSON>, TUtils>;
```

### transaction

```ts
transaction: TransactionWithMutations<T, "update">;
```
