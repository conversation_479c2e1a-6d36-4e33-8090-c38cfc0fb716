---
id: GetResult
title: GetResult
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: GetResult\<TContext\>

```ts
type GetResult<TContext> = Prettify<TContext["result"] extends object ? TContext["result"] : TContext["hasJoins"] extends true ? TContext["schema"] : TContext["schema"][TContext["fromSourceName"]]>;
```

Defined in: [packages/db/src/query/builder/types.ts:208](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L208)

## Type Parameters

• **TContext** *extends* [`Context`](../../interfaces/context.md)
