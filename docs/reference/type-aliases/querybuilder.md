---
id: QueryBuilder
title: QueryBuilder
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: QueryBuilder\<TContext\>

```ts
type QueryBuilder<TContext> = Omit<BaseQueryBuilder<TContext>, "from" | "_getQuery">;
```

Defined in: [packages/db/src/query/builder/index.ts:760](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L760)

## Type Parameters

• **TContext** *extends* [`Context`](../../interfaces/context.md)
