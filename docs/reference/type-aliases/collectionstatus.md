---
id: CollectionStatus
title: CollectionStatus
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Type Alias: CollectionStatus

```ts
type CollectionStatus = "idle" | "loading" | "initialCommit" | "ready" | "error" | "cleaned-up";
```

Defined in: [packages/db/src/types.ts:335](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L335)

Collection status values for lifecycle management

## Examples

```ts
// Check collection status
if (collection.status === "loading") {
  console.log("Collection is loading initial data")
} else if (collection.status === "ready") {
  console.log("Collection is ready for use")
}
```

```ts
// Status transitions
// idle → loading → initialCommit → ready
// Any status can transition to → error or cleaned-up
```
