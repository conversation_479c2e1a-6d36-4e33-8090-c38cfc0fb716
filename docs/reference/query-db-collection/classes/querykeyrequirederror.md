---
id: Query<PERSON>eyRequiredError
title: Query<PERSON>eyRequiredError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: QueryKeyRequiredError

Defined in: [packages/query-db-collection/src/errors.ts:11](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/errors.ts#L11)

## Extends

- [`QueryCollectionError`](../querycollectionerror.md)

## Constructors

### new QueryKeyRequiredError()

```ts
new QueryKeyRequiredError(): QueryKeyRequiredError
```

Defined in: [packages/query-db-collection/src/errors.ts:12](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/errors.ts#L12)

#### Returns

[`QueryKeyRequiredError`](../querykeyrequirederror.md)

#### Overrides

[`QueryCollectionError`](../querycollectionerror.md).[`constructor`](../QueryCollectionError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`cause`](../QueryCollectionError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`message`](../QueryCollectionError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`name`](../QueryCollectionError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`stack`](../QueryCollectionError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`prepareStackTrace`](../QueryCollectionError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`stackTraceLimit`](../QueryCollectionError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`QueryCollectionError`](../querycollectionerror.md).[`captureStackTrace`](../QueryCollectionError.md#capturestacktrace)
