---
id: QueryCollectionConfig
title: QueryCollectionConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: QueryCollectionConfig\<TItem, TError, T<PERSON><PERSON><PERSON><PERSON><PERSON>\>

Defined in: [packages/query-db-collection/src/query.ts:32](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L32)

## Type Parameters

• **TItem** *extends* `object`

• **TError** = `unknown`

• **TQueryKey** *extends* `QueryKey` = `QueryKey`

## Properties

### enabled?

```ts
optional enabled: boolean;
```

Defined in: [packages/query-db-collection/src/query.ts:42](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L42)

***

### getKey()

```ts
getKey: (item) => string | number;
```

Defined in: [packages/query-db-collection/src/query.ts:74](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L74)

#### Parameters

##### item

`TItem`

#### Returns

`string` \| `number`

***

### id?

```ts
optional id: string;
```

Defined in: [packages/query-db-collection/src/query.ts:73](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L73)

***

### meta?

```ts
optional meta: Record<string, unknown>;
```

Defined in: [packages/query-db-collection/src/query.ts:242](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L242)

Metadata to pass to the query.
Available in queryFn via context.meta

#### Example

```ts
// Using meta for error context
queryFn: async (context) => {
  try {
    return await api.getTodos(userId)
  } catch (error) {
    // Use meta for better error messages
    throw new Error(
      context.meta?.errorMessage || 'Failed to load todos'
    )
  }
},
meta: {
  errorMessage: `Failed to load todos for user ${userId}`
}
```

***

### onDelete?

```ts
optional onDelete: DeleteMutationFn<TItem>;
```

Defined in: [packages/query-db-collection/src/query.ts:219](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L219)

Optional asynchronous handler function called before a delete operation

#### Param

Object containing transaction and collection information

#### Returns

Promise resolving to void or { refetch?: boolean } to control refetching

#### Examples

```ts
// Basic query collection delete handler
onDelete: async ({ transaction }) => {
  const mutation = transaction.mutations[0]
  await api.deleteTodo(mutation.original.id)
  // Automatically refetches query after delete
}
```

```ts
// Delete handler with refetch control
onDelete: async ({ transaction }) => {
  const mutation = transaction.mutations[0]
  await api.deleteTodo(mutation.original.id)
  return { refetch: false } // Skip automatic refetch
}
```

```ts
// Delete handler with multiple items
onDelete: async ({ transaction }) => {
  const keysToDelete = transaction.mutations.map(m => m.key)
  await api.deleteTodos(keysToDelete)
  // Will refetch query to get updated data
}
```

```ts
// Delete handler with related collection refetch
onDelete: async ({ transaction, collection }) => {
  const mutation = transaction.mutations[0]
  await api.deleteTodo(mutation.original.id)

  // Refetch related collections when this item is deleted
  await Promise.all([
    collection.utils.refetch(), // Refetch this collection
    usersCollection.utils.refetch(), // Refetch users
    projectsCollection.utils.refetch() // Refetch projects
  ])

  return { refetch: false } // Skip automatic refetch since we handled it manually
}
```

***

### onInsert?

```ts
optional onInsert: InsertMutationFn<TItem>;
```

Defined in: [packages/query-db-collection/src/query.ts:120](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L120)

Optional asynchronous handler function called before an insert operation

#### Param

Object containing transaction and collection information

#### Returns

Promise resolving to void or { refetch?: boolean } to control refetching

#### Examples

```ts
// Basic query collection insert handler
onInsert: async ({ transaction }) => {
  const newItem = transaction.mutations[0].modified
  await api.createTodo(newItem)
  // Automatically refetches query after insert
}
```

```ts
// Insert handler with refetch control
onInsert: async ({ transaction }) => {
  const newItem = transaction.mutations[0].modified
  await api.createTodo(newItem)
  return { refetch: false } // Skip automatic refetch
}
```

```ts
// Insert handler with multiple items
onInsert: async ({ transaction }) => {
  const items = transaction.mutations.map(m => m.modified)
  await api.createTodos(items)
  // Will refetch query to get updated data
}
```

```ts
// Insert handler with error handling
onInsert: async ({ transaction }) => {
  try {
    const newItem = transaction.mutations[0].modified
    await api.createTodo(newItem)
  } catch (error) {
    console.error('Insert failed:', error)
    throw error // Transaction will rollback optimistic changes
  }
}
```

***

### onUpdate?

```ts
optional onUpdate: UpdateMutationFn<TItem>;
```

Defined in: [packages/query-db-collection/src/query.ts:173](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L173)

Optional asynchronous handler function called before an update operation

#### Param

Object containing transaction and collection information

#### Returns

Promise resolving to void or { refetch?: boolean } to control refetching

#### Examples

```ts
// Basic query collection update handler
onUpdate: async ({ transaction }) => {
  const mutation = transaction.mutations[0]
  await api.updateTodo(mutation.original.id, mutation.changes)
  // Automatically refetches query after update
}
```

```ts
// Update handler with multiple items
onUpdate: async ({ transaction }) => {
  const updates = transaction.mutations.map(m => ({
    id: m.key,
    changes: m.changes
  }))
  await api.updateTodos(updates)
  // Will refetch query to get updated data
}
```

```ts
// Update handler with manual refetch
onUpdate: async ({ transaction, collection }) => {
  const mutation = transaction.mutations[0]
  await api.updateTodo(mutation.original.id, mutation.changes)

  // Manually trigger refetch
  await collection.utils.refetch()

  return { refetch: false } // Skip automatic refetch
}
```

```ts
// Update handler with related collection refetch
onUpdate: async ({ transaction, collection }) => {
  const mutation = transaction.mutations[0]
  await api.updateTodo(mutation.original.id, mutation.changes)

  // Refetch related collections when this item changes
  await Promise.all([
    collection.utils.refetch(), // Refetch this collection
    usersCollection.utils.refetch(), // Refetch users
    tagsCollection.utils.refetch() // Refetch tags
  ])

  return { refetch: false } // Skip automatic refetch since we handled it manually
}
```

***

### queryClient

```ts
queryClient: QueryClient;
```

Defined in: [packages/query-db-collection/src/query.ts:39](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L39)

***

### queryFn()

```ts
queryFn: (context) => Promise<TItem[]>;
```

Defined in: [packages/query-db-collection/src/query.ts:38](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L38)

#### Parameters

##### context

###### client

`QueryClient`

###### direction?

`unknown`

**Deprecated**

if you want access to the direction, you can add it to the pageParam

###### meta

`undefined` \| `Record`\<`string`, `unknown`\>

###### pageParam?

`unknown`

###### queryKey

`TQueryKey`

###### signal

`AbortSignal`

#### Returns

`Promise`\<`TItem`[]\>

***

### queryKey

```ts
queryKey: TQueryKey;
```

Defined in: [packages/query-db-collection/src/query.ts:37](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L37)

***

### refetchInterval?

```ts
optional refetchInterval: number | false | (query) => undefined | number | false;
```

Defined in: [packages/query-db-collection/src/query.ts:43](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L43)

***

### retry?

```ts
optional retry: RetryValue<TError>;
```

Defined in: [packages/query-db-collection/src/query.ts:50](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L50)

***

### retryDelay?

```ts
optional retryDelay: RetryDelayValue<TError>;
```

Defined in: [packages/query-db-collection/src/query.ts:57](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L57)

***

### schema?

```ts
optional schema: StandardSchemaV1<unknown, unknown>;
```

Defined in: [packages/query-db-collection/src/query.ts:75](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L75)

***

### staleTime?

```ts
optional staleTime: StaleTimeFunction<TItem[], TError, TItem[], TQueryKey>;
```

Defined in: [packages/query-db-collection/src/query.ts:64](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L64)

***

### startSync?

```ts
optional startSync: boolean;
```

Defined in: [packages/query-db-collection/src/query.ts:77](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L77)

***

### sync?

```ts
optional sync: SyncConfig<TItem, string | number>;
```

Defined in: [packages/query-db-collection/src/query.ts:76](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L76)
