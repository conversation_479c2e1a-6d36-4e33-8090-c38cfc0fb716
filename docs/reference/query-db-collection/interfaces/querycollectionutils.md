---
id: QueryCollectionUtils
title: QueryCollectionUtils
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: QueryCollectionUtils\<TItem, TKey, TInsertInput\>

Defined in: [packages/query-db-collection/src/query.ts:256](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L256)

Write operation types for batch operations

## Extends

- `UtilsRecord`

## Type Parameters

• **TItem** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TInsertInput** *extends* `object` = `TItem`

## Indexable

```ts
[key: string]: Fn
```

## Properties

### refetch

```ts
refetch: RefetchFn;
```

Defined in: [packages/query-db-collection/src/query.ts:261](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L261)

***

### writeBatch()

```ts
writeBatch: (operations) => void;
```

Defined in: [packages/query-db-collection/src/query.ts:266](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L266)

#### Parameters

##### operations

[`SyncOperation`](../../type-aliases/syncoperation.md)\<`TItem`, `TKey`, `TInsertInput`\>[]

#### Returns

`void`

***

### writeDelete()

```ts
writeDelete: (keys) => void;
```

Defined in: [packages/query-db-collection/src/query.ts:264](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L264)

#### Parameters

##### keys

`TKey` | `TKey`[]

#### Returns

`void`

***

### writeInsert()

```ts
writeInsert: (data) => void;
```

Defined in: [packages/query-db-collection/src/query.ts:262](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L262)

#### Parameters

##### data

`TInsertInput` | `TInsertInput`[]

#### Returns

`void`

***

### writeUpdate()

```ts
writeUpdate: (updates) => void;
```

Defined in: [packages/query-db-collection/src/query.ts:263](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L263)

#### Parameters

##### updates

`Partial`\<`TItem`\> | `Partial`\<`TItem`\>[]

#### Returns

`void`

***

### writeUpsert()

```ts
writeUpsert: (data) => void;
```

Defined in: [packages/query-db-collection/src/query.ts:265](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L265)

#### Parameters

##### data

`Partial`\<`TItem`\> | `Partial`\<`TItem`\>[]

#### Returns

`void`
