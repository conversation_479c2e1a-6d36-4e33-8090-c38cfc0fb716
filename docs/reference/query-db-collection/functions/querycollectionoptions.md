---
id: queryCollectionOptions
title: queryCollectionOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: queryCollectionOptions()

```ts
function queryCollectionOptions<TItem, TE<PERSON>r, T<PERSON><PERSON><PERSON><PERSON>ey, T<PERSON><PERSON>, TInsertInput>(config): CollectionConfig<TItem, string | number, StandardSchemaV1<unknown, unknown>, TItem> & object
```

Defined in: [packages/query-db-collection/src/query.ts:277](https://github.com/TanStack/db/blob/main/packages/query-db-collection/src/query.ts#L277)

Creates query collection options for use with a standard Collection

## Type Parameters

• **TItem** *extends* `object`

• **TError** = `unknown`

• **TQueryKey** *extends* readonly `unknown`[] = readonly `unknown`[]

• **TKey** *extends* `string` \| `number` = `string` \| `number`

• **TInsertInput** *extends* `object` = `TItem`

## Parameters

### config

[`QueryCollectionConfig`](../../interfaces/querycollectionconfig.md)\<`TItem`, `TError`, `TQueryKey`\>

Configuration options for the Query collection

## Returns

`CollectionConfig`\<`TItem`, `string` \| `number`, `StandardSchemaV1`\<`unknown`, `unknown`\>, `TItem`\> & `object`

Collection options with utilities
