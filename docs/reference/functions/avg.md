---
id: avg
title: avg
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: avg()

```ts
function avg(arg): Aggregate<number>
```

Defined in: [packages/db/src/query/builder/functions.ts:229](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L229)

## Parameters

### arg

`number` | `BasicExpression`\<`number`\> | `RefProxy`\<`number`\> | `RefProxy`\<`undefined` \| `number`\>

## Returns

`Aggregate`\<`number`\>
