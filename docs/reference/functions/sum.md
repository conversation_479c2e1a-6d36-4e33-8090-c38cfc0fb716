---
id: sum
title: sum
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: sum()

```ts
function sum(arg): Aggregate<number>
```

Defined in: [packages/db/src/query/builder/functions.ts:239](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L239)

## Parameters

### arg

`number` | `BasicExpression`\<`number`\> | `RefProxy`\<`number`\> | `RefProxy`\<`undefined` \| `number`\>

## Returns

`Aggregate`\<`number`\>
