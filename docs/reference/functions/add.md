---
id: add
title: add
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: add()

```ts
function add(left, right): BasicExpression<number>
```

Defined in: [packages/db/src/query/builder/functions.ts:208](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L208)

## Parameters

### left

`number` | `BasicExpression`\<`number`\> | `RefProxy`\<`number`\> | `RefProxy`\<`undefined` \| `number`\>

### right

`number` | `BasicExpression`\<`number`\> | `RefProxy`\<`number`\> | `RefProxy`\<`undefined` \| `number`\>

## Returns

`BasicExpression`\<`number`\>
