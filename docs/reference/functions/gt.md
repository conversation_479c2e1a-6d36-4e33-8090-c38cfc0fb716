---
id: gt
title: gt
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: gt()

## Call Signature

```ts
function gt<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:24](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L24)

### Type Parameters

• **T**

### Parameters

#### left

`RefProxy`\<`T`\>

#### right

`T` | `RefProxy`\<`T`\> | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function gt<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:28](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L28)

### Type Parameters

• **T** *extends* `string` \| `number`

### Parameters

#### left

`T` | `BasicExpression`\<`T`\>

#### right

`T` | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function gt<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:32](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L32)

### Type Parameters

• **T**

### Parameters

#### left

`Aggregate`\<`T`\>

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>
