---
id: inArray
title: inArray
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: inArray()

```ts
function inArray(value, array): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:124](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L124)

## Parameters

### value

`any`

### array

`any`

## Returns

`BasicExpression`\<`boolean`\>
