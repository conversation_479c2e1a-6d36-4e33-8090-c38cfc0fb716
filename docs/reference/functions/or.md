---
id: or
title: or
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: or()

## Call Signature

```ts
function or(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:99](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L99)

### Parameters

#### left

`any`

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function or(
   left, 
   right, ...
rest): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:103](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L103)

### Parameters

#### left

`any`

#### right

`any`

#### rest

...`any`[]

### Returns

`BasicExpression`\<`boolean`\>
