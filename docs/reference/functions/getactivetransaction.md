---
id: getActiveTransaction
title: getActiveTransaction
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: getActiveTransaction()

```ts
function getActiveTransaction(): 
  | undefined
| Transaction<Record<string, unknown>>
```

Defined in: [packages/db/src/transactions.ts:93](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L93)

Gets the currently active ambient transaction, if any
Used internally by collection operations to join existing transactions

## Returns

  \| `undefined`
  \| [`Transaction`](../../classes/transaction.md)\<`Record`\<`string`, `unknown`\>\>

The active transaction or undefined if none is active

## Example

```ts
// Check if operations will join an ambient transaction
const ambientTx = getActiveTransaction()
if (ambientTx) {
  console.log('Operations will join transaction:', ambientTx.id)
}
```
