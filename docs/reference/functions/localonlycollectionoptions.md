---
id: localOnlyCollectionOptions
title: localOnlyCollectionOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: localOnlyCollectionOptions()

```ts
function localOnlyCollectionOptions<TExplicit, TSchema, TF<PERSON>back, TKey>(config): object
```

Defined in: [packages/db/src/local-only.ts:137](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L137)

Creates Local-only collection options for use with a standard Collection

This is an in-memory collection that doesn't sync with external sources but uses a loopback sync config
that immediately "syncs" all optimistic changes to the collection, making them permanent.
Perfect for local-only data that doesn't need persistence or external synchronization.

## Type Parameters

• **TExplicit** = `unknown`

The explicit type of items in the collection (highest priority)

• **TSchema** *extends* `StandardSchemaV1`\<`unknown`, `unknown`\> = `never`

The schema type for validation and type inference (second priority)

• **TFallback** *extends* `Record`\<`string`, `unknown`\> = `Record`\<`string`, `unknown`\>

The fallback type if no explicit or schema type is provided

• **TKey** *extends* `string` \| `number` = `string` \| `number`

The type of the key returned by getKey

## Parameters

### config

[`LocalOnlyCollectionConfig`](../../interfaces/localonlycollectionconfig.md)\<`TExplicit`, `TSchema`, `TFallback`, `TKey`\>

Configuration options for the Local-only collection

## Returns

`object`

Collection options with utilities (currently empty but follows the pattern)

### gcTime

```ts
gcTime: number = 0;
```

### getKey()

```ts
getKey: (item) => TKey;
```

#### Parameters

##### item

[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>

#### Returns

`TKey`

### id?

```ts
optional id: string;
```

Standard Collection configuration properties

### onDelete()

```ts
onDelete: (params) => Promise<any> = wrappedOnDelete;
```

Wrapper for onDelete handler that also confirms the transaction immediately

#### Parameters

##### params

[`DeleteMutationFnParams`](../../type-aliases/deletemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, [`LocalOnlyCollectionUtils`](../../interfaces/localonlycollectionutils.md)\>

#### Returns

`Promise`\<`any`\>

### onInsert()

```ts
onInsert: (params) => Promise<any> = wrappedOnInsert;
```

Create wrapper handlers that call user handlers first, then confirm transactions
Wraps the user's onInsert handler to also confirm the transaction immediately

#### Parameters

##### params

[`InsertMutationFnParams`](../../type-aliases/insertmutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, [`LocalOnlyCollectionUtils`](../../interfaces/localonlycollectionutils.md)\>

#### Returns

`Promise`\<`any`\>

### onUpdate()

```ts
onUpdate: (params) => Promise<any> = wrappedOnUpdate;
```

Wrapper for onUpdate handler that also confirms the transaction immediately

#### Parameters

##### params

[`UpdateMutationFnParams`](../../type-aliases/updatemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, [`LocalOnlyCollectionUtils`](../../interfaces/localonlycollectionutils.md)\>

#### Returns

`Promise`\<`any`\>

### schema?

```ts
optional schema: TSchema;
```

### startSync

```ts
startSync: boolean = true;
```

### sync

```ts
sync: SyncConfig<ResolveType<TExplicit, TSchema, TFallback>, TKey> = syncResult.sync;
```

### utils

```ts
utils: LocalOnlyCollectionUtils;
```

## Examples

```ts
// Basic local-only collection
const collection = createCollection(
  localOnlyCollectionOptions({
    getKey: (item) => item.id,
  })
)
```

```ts
// Local-only collection with initial data
const collection = createCollection(
  localOnlyCollectionOptions({
    getKey: (item) => item.id,
    initialData: [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
    ],
  })
)
```

```ts
// Local-only collection with mutation handlers
const collection = createCollection(
  localOnlyCollectionOptions({
    getKey: (item) => item.id,
    onInsert: async ({ transaction }) => {
      console.log('Item inserted:', transaction.mutations[0].modified)
      // Custom logic after insert
    },
  })
)
```
