---
id: compileQuery
title: compileQuery
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: compileQuery()

```ts
function compileQuery(
   rawQuery, 
   inputs, 
   cache, 
   queryMapping): CompilationResult
```

Defined in: [packages/db/src/query/compiler/index.ts:46](https://github.com/TanStack/db/blob/main/packages/db/src/query/compiler/index.ts#L46)

Compiles a query2 IR into a D2 pipeline

## Parameters

### rawQuery

`QueryIR`

The query IR to compile

### inputs

`Record`\<`string`, [`KeyedStream`](../../type-aliases/keyedstream.md)\>

Mapping of collection names to input streams

### cache

`QueryCache` = `...`

Optional cache for compiled subqueries (used internally for recursion)

### queryMapping

`QueryMapping` = `...`

Optional mapping from optimized queries to original queries

## Returns

`CompilationResult`

A CompilationResult with the pipeline and collection WHERE clauses
