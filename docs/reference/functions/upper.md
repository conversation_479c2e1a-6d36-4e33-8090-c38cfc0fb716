---
id: upper
title: upper
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: upper()

```ts
function upper(arg): BasicExpression<string>
```

Defined in: [packages/db/src/query/builder/functions.ts:158](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L158)

## Parameters

### arg

`string` | `RefProxy`\<`string`\> | `RefProxy`\<`undefined` \| `string`\> | `BasicExpression`\<`string`\>

## Returns

`BasicExpression`\<`string`\>
