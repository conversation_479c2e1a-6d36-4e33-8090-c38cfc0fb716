---
id: createOptimisticAction
title: createOptimisticAction
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: createOptimisticAction()

```ts
function createOptimisticAction<TVariables>(options): (variables) => Transaction
```

Defined in: [packages/db/src/optimistic-action.ts:41](https://github.com/TanStack/db/blob/main/packages/db/src/optimistic-action.ts#L41)

Creates an optimistic action function that applies local optimistic updates immediately
before executing the actual mutation on the server.

This pattern allows for responsive UI updates while the actual mutation is in progress.
The optimistic update is applied via the `onMutate` callback, and the server mutation
is executed via the `mutationFn`.

## Type Parameters

• **TVariables** = `unknown`

The type of variables that will be passed to the action function

## Parameters

### options

[`CreateOptimisticActionsOptions`](../../interfaces/createoptimisticactionsoptions.md)\<`TVariables`\>

Configuration options for the optimistic action

## Returns

`Function`

A function that accepts variables of type TVariables and returns a Transaction

### Parameters

#### variables

`TVariables`

### Returns

[`Transaction`](../../classes/transaction.md)

## Example

```ts
const addTodo = createOptimisticAction<string>({
  onMutate: (text) => {
    // Instantly applies local optimistic state
    todoCollection.insert({
      id: uuid(),
      text,
      completed: false
    })
  },
  mutationFn: async (text, params) => {
    // Persist the todo to your backend
    const response = await fetch('/api/todos', {
      method: 'POST',
      body: JSON.stringify({ text, completed: false }),
    })
    return response.json()
  }
})

// Usage
const transaction = addTodo('New Todo Item')
```
