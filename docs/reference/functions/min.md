---
id: min
title: min
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: min()

```ts
function min(arg): Aggregate<number>
```

Defined in: [packages/db/src/query/builder/functions.ts:249](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L249)

## Parameters

### arg

`number` | `BasicExpression`\<`number`\> | `RefProxy`\<`number`\> | `RefProxy`\<`undefined` \| `number`\>

## Returns

`Aggregate`\<`number`\>
