---
id: lt
title: lt
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: lt()

## Call Signature

```ts
function lt<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:50](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L50)

### Type Parameters

• **T**

### Parameters

#### left

`RefProxy`\<`T`\>

#### right

`T` | `RefProxy`\<`T`\> | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function lt<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:54](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L54)

### Type Parameters

• **T** *extends* `string` \| `number`

### Parameters

#### left

`T` | `BasicExpression`\<`T`\>

#### right

`T` | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function lt<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:58](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L58)

### Type Parameters

• **T**

### Parameters

#### left

`Aggregate`\<`T`\>

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>
