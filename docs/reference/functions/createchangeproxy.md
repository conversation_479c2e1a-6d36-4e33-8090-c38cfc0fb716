---
id: createChangeProxy
title: createChangeProxy
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: createChangeProxy()

```ts
function createChangeProxy<T>(target, parent?): object
```

Defined in: [packages/db/src/proxy.ts:273](https://github.com/TanStack/db/blob/main/packages/db/src/proxy.ts#L273)

Creates a proxy that tracks changes to the target object

## Type Parameters

• **T** *extends* `Record`\<`string` \| `symbol`, `any`\>

## Parameters

### target

`T`

The object to proxy

### parent?

Optional parent information

#### prop

`string` \| `symbol`

#### tracker

`ChangeTracker`\<`Record`\<`string` \| `symbol`, `unknown`\>\>

## Returns

`object`

An object containing the proxy and a function to get the changes

### getChanges()

```ts
getChanges: () => Record<string | symbol, any>;
```

#### Returns

`Record`\<`string` \| `symbol`, `any`\>

### proxy

```ts
proxy: T;
```
