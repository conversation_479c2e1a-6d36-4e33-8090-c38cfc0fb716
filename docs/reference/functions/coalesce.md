---
id: coalesce
title: coalesce
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: coalesce()

```ts
function coalesce(...args): BasicExpression<any>
```

Defined in: [packages/db/src/query/builder/functions.ts:201](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L201)

## Parameters

### args

...`any`[]

## Returns

`BasicExpression`\<`any`\>
