---
id: ilike
title: ilike
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: ilike()

```ts
function ilike(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:144](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L144)

## Parameters

### left

`string` | `RefProxy`\<`string`\> | `RefProxy`\<`null` \| `string`\> | `RefProxy`\<`undefined` \| `string`\> | `BasicExpression`\<`string`\>

### right

`string` | `RefProxy`\<`string`\> | `BasicExpression`\<`string`\>

## Returns

`BasicExpression`\<`boolean`\>
