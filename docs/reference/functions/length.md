---
id: length
title: length
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: length()

```ts
function length(arg): BasicExpression<number>
```

Defined in: [packages/db/src/query/builder/functions.ts:178](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L178)

## Parameters

### arg

`string` | `any`[] | `RefProxy`\<`string`\> | `RefProxy`\<`undefined` \| `string`\> | `BasicExpression`\<`string`\> | `RefProxy`\<`any`[]\> | `RefProxy`\<`undefined` \| `any`[]\> | `BasicExpression`\<`any`[]\>

## Returns

`BasicExpression`\<`number`\>
