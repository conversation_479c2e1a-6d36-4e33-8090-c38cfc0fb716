---
id: liveQueryCollectionOptions
title: liveQueryCollectionOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: liveQueryCollectionOptions()

```ts
function liveQueryCollectionOptions<TContext, TResult>(config): CollectionConfig<TResult>
```

Defined in: [packages/db/src/query/live-query-collection.ts:117](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L117)

Creates live query collection options for use with createCollection

## Type Parameters

• **TContext** *extends* [`Context`](../../interfaces/context.md)

• **TResult** *extends* `object` = \{ \[K in string \| number \| symbol\]: (TContext\["result"\] extends object ? any\[any\] : TContext\["hasJoins"\] extends true ? TContext\["schema"\] : TContext\["schema"\]\[TContext\["fromSourceName"\]\])\[K\] \}

## Parameters

### config

[`LiveQueryCollectionConfig`](../../interfaces/livequerycollectionconfig.md)\<`TContext`, `TResult`\>

Configuration options for the live query collection

## Returns

[`CollectionConfig`](../../interfaces/collectionconfig.md)\<`TResult`\>

Collection options that can be passed to createCollection

## Example

```typescript
const options = liveQueryCollectionOptions({
  // id is optional - will auto-generate if not provided
  query: (q) => q
    .from({ post: postsCollection })
    .where(({ post }) => eq(post.published, true))
    .select(({ post }) => ({
      id: post.id,
      title: post.title,
      content: post.content,
    })),
  // getKey is optional - will use stream key if not provided
})

const collection = createCollection(options)
```
