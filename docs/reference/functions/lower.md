---
id: lower
title: lower
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: lower()

```ts
function lower(arg): BasicExpression<string>
```

Defined in: [packages/db/src/query/builder/functions.ts:168](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L168)

## Parameters

### arg

`string` | `RefProxy`\<`string`\> | `RefProxy`\<`undefined` \| `string`\> | `BasicExpression`\<`string`\>

## Returns

`BasicExpression`\<`string`\>
