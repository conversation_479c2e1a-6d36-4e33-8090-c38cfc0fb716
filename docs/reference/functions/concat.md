---
id: concat
title: concat
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: concat()

```ts
function concat(...args): BasicExpression<string>
```

Defined in: [packages/db/src/query/builder/functions.ts:192](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L192)

## Parameters

### args

...`any`[]

## Returns

`BasicExpression`\<`string`\>
