---
id: eq
title: eq
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: eq()

## Call Signature

```ts
function eq<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:11](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L11)

### Type Parameters

• **T**

### Parameters

#### left

`RefProxy`\<`T`\>

#### right

`T` | `RefProxy`\<`T`\> | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function eq<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:15](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L15)

### Type Parameters

• **T** *extends* `string` \| `number` \| `boolean`

### Parameters

#### left

`T` | `BasicExpression`\<`T`\>

#### right

`T` | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function eq<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:19](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L19)

### Type Parameters

• **T**

### Parameters

#### left

`Aggregate`\<`T`\>

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>
