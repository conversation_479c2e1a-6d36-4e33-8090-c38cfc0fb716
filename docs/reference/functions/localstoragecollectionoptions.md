---
id: localStorageCollectionOptions
title: localStorageCollectionOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: localStorageCollectionOptions()

```ts
function localStorageCollectionOptions<TExplicit, TSchema, TFallback>(config): object
```

Defined in: [packages/db/src/local-storage.ts:205](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L205)

Creates localStorage collection options for use with a standard Collection

This function creates a collection that persists data to localStorage/sessionStorage
and synchronizes changes across browser tabs using storage events.

## Type Parameters

• **TExplicit** = `unknown`

The explicit type of items in the collection (highest priority)

• **TSchema** *extends* `StandardSchemaV1`\<`unknown`, `unknown`\> = `never`

The schema type for validation and type inference (second priority)

• **TFallback** *extends* `object` = `Record`\<`string`, `unknown`\>

The fallback type if no explicit or schema type is provided

## Parameters

### config

[`LocalStorageCollectionConfig`](../../interfaces/localstoragecollectionconfig.md)\<`TExplicit`, `TSchema`, `TFallback`\>

Configuration options for the localStorage collection

## Returns

`object`

Collection options with utilities including clearStorage and getStorageSize

### getKey()

```ts
getKey: (item) => string | number;
```

#### Parameters

##### item

[`ResolveType`](../../type-aliases/resolvetype.md)

#### Returns

`string` \| `number`

### id

```ts
id: string = collectionId;
```

### onDelete()

```ts
onDelete: (params) => Promise<any> = wrappedOnDelete;
```

#### Parameters

##### params

[`DeleteMutationFnParams`](../../type-aliases/deletemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

#### Returns

`Promise`\<`any`\>

### onInsert()

```ts
onInsert: (params) => Promise<any> = wrappedOnInsert;
```

#### Parameters

##### params

[`InsertMutationFnParams`](../../type-aliases/insertmutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

#### Returns

`Promise`\<`any`\>

### onUpdate()

```ts
onUpdate: (params) => Promise<any> = wrappedOnUpdate;
```

#### Parameters

##### params

[`UpdateMutationFnParams`](../../type-aliases/updatemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

#### Returns

`Promise`\<`any`\>

### schema?

```ts
optional schema: TSchema;
```

### sync

```ts
sync: SyncConfig<ResolveType<TExplicit, TSchema, TFallback>, string | number> & object;
```

#### Type declaration

##### manualTrigger()?

```ts
optional manualTrigger: () => void;
```

###### Returns

`void`

### utils

```ts
utils: object;
```

#### utils.clearStorage

```ts
clearStorage: ClearStorageFn;
```

#### utils.getStorageSize

```ts
getStorageSize: GetStorageSizeFn;
```

## Examples

```ts
// Basic localStorage collection
const collection = createCollection(
  localStorageCollectionOptions({
    storageKey: 'todos',
    getKey: (item) => item.id,
  })
)
```

```ts
// localStorage collection with custom storage
const collection = createCollection(
  localStorageCollectionOptions({
    storageKey: 'todos',
    storage: window.sessionStorage, // Use sessionStorage instead
    getKey: (item) => item.id,
  })
)
```

```ts
// localStorage collection with mutation handlers
const collection = createCollection(
  localStorageCollectionOptions({
    storageKey: 'todos',
    getKey: (item) => item.id,
    onInsert: async ({ transaction }) => {
      console.log('Item inserted:', transaction.mutations[0].modified)
    },
  })
)
```
