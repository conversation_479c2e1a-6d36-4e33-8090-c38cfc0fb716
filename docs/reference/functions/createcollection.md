---
id: createCollection
title: createCollection
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: createCollection()

```ts
function createCollection<TExplicit, T<PERSON><PERSON>, T<PERSON><PERSON>s, TSchema, TFallback>(options): Collection<ResolveType<TExplicit, TSchema, TFallback>, T<PERSON>ey, TUtils, TSchema, ResolveInsertInput<TExplicit, TSchema, TFallback>>
```

Defined in: [packages/db/src/collection.ts:160](https://github.com/TanStack/db/blob/main/packages/db/src/collection.ts#L160)

Creates a new Collection instance with the given configuration

## Type Parameters

• **TExplicit** = `unknown`

The explicit type of items in the collection (highest priority)

• **TKey** *extends* `string` \| `number` = `string` \| `number`

The type of the key for the collection

• **TUtils** *extends* [`UtilsRecord`](../../type-aliases/utilsrecord.md) = \{\}

The utilities record type

• **TSchema** *extends* `StandardSchemaV1`\<`unknown`, `unknown`\> = `StandardSchemaV1`\<`unknown`, `unknown`\>

The schema type for validation and type inference (second priority)

• **TFallback** *extends* `object` = `Record`\<`string`, `unknown`\>

The fallback type if no explicit or schema type is provided

## Parameters

### options

[`CollectionConfig`](../../interfaces/collectionconfig.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, `TSchema`, [`ResolveInsertInput`](../../type-aliases/resolveinsertinput.md)\<`TExplicit`, `TSchema`, `TFallback`\>\> & `object`

Collection options with optional utilities

## Returns

[`Collection`](../../interfaces/collection.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, `TUtils`, `TSchema`, [`ResolveInsertInput`](../../type-aliases/resolveinsertinput.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

A new Collection with utilities exposed both at top level and under .utils

## Examples

```ts
// Pattern 1: With operation handlers (direct collection calls)
const todos = createCollection({
  id: "todos",
  getKey: (todo) => todo.id,
  schema,
  onInsert: async ({ transaction, collection }) => {
    // Send to API
    await api.createTodo(transaction.mutations[0].modified)
  },
  onUpdate: async ({ transaction, collection }) => {
    await api.updateTodo(transaction.mutations[0].modified)
  },
  onDelete: async ({ transaction, collection }) => {
    await api.deleteTodo(transaction.mutations[0].key)
  },
  sync: { sync: () => {} }
})

// Direct usage (handlers manage transactions)
const tx = todos.insert({ id: "1", text: "Buy milk", completed: false })
await tx.isPersisted.promise
```

```ts
// Pattern 2: Manual transaction management
const todos = createCollection({
  getKey: (todo) => todo.id,
  schema: todoSchema,
  sync: { sync: () => {} }
})

// Explicit transaction usage
const tx = createTransaction({
  mutationFn: async ({ transaction }) => {
    // Handle all mutations in transaction
    await api.saveChanges(transaction.mutations)
  }
})

tx.mutate(() => {
  todos.insert({ id: "1", text: "Buy milk" })
  todos.update("2", draft => { draft.completed = true })
})

await tx.isPersisted.promise
```

```ts
// Using schema for type inference (preferred as it also gives you client side validation)
const todoSchema = z.object({
  id: z.string(),
  title: z.string(),
  completed: z.boolean()
})

const todos = createCollection({
  schema: todoSchema,
  getKey: (todo) => todo.id,
  sync: { sync: () => {} }
})

// Note: You must provide either an explicit type or a schema, but not both.
```
