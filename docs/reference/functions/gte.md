---
id: gte
title: gte
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: gte()

## Call Signature

```ts
function gte<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:37](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L37)

### Type Parameters

• **T**

### Parameters

#### left

`RefProxy`\<`T`\>

#### right

`T` | `RefProxy`\<`T`\> | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function gte<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:41](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L41)

### Type Parameters

• **T** *extends* `string` \| `number`

### Parameters

#### left

`T` | `BasicExpression`\<`T`\>

#### right

`T` | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function gte<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:45](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L45)

### Type Parameters

• **T**

### Parameters

#### left

`Aggregate`\<`T`\>

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>
