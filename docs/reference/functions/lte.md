---
id: lte
title: lte
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: lte()

## Call Signature

```ts
function lte<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:63](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L63)

### Type Parameters

• **T**

### Parameters

#### left

`RefProxy`\<`T`\>

#### right

`T` | `RefProxy`\<`T`\> | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function lte<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:67](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L67)

### Type Parameters

• **T** *extends* `string` \| `number`

### Parameters

#### left

`T` | `BasicExpression`\<`T`\>

#### right

`T` | `BasicExpression`\<`T`\>

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function lte<T>(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:71](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L71)

### Type Parameters

• **T**

### Parameters

#### left

`Aggregate`\<`T`\>

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>
