---
id: withArrayChangeTracking
title: withArrayChangeTracking
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: withArrayChangeTracking()

```ts
function withArrayChangeTracking<T>(targets, callback): Record<string | symbol, unknown>[]
```

Defined in: [packages/db/src/proxy.ts:1005](https://github.com/TanStack/db/blob/main/packages/db/src/proxy.ts#L1005)

Creates proxies for an array of objects, passes them to a callback function,
and returns the changes made by the callback for each object

## Type Parameters

• **T** *extends* `object`

## Parameters

### targets

`T`[]

Array of objects to proxy

### callback

(`proxies`) => `void`

Function that receives the proxies and can make changes to them

## Returns

`Record`\<`string` \| `symbol`, `unknown`\>[]

Array of changes made to each object
