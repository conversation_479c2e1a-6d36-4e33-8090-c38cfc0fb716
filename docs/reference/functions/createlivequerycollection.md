---
id: createLiveQueryCollection
title: createLiveQueryCollection
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: createLiveQueryCollection()

## Call Signature

```ts
function createLiveQueryCollection<TContext, TResult>(query): Collection<TResult, string | number, {}>
```

Defined in: [packages/db/src/query/live-query-collection.ts:424](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L424)

Creates a live query collection directly

### Type Parameters

• **TContext** *extends* [`Context`](../../interfaces/context.md)

• **TResult** *extends* `object` = \{ \[K in string \| number \| symbol\]: (TContext\["result"\] extends object ? any\[any\] : TContext\["hasJoins"\] extends true ? TContext\["schema"\] : TContext\["schema"\]\[TContext\["fromSourceName"\]\])\[K\] \}

### Parameters

#### query

(`q`) => [`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

### Returns

[`Collection`](../../interfaces/collection.md)\<`TResult`, `string` \| `number`, \{\}\>

### Example

```typescript
// Minimal usage - just pass a query function
const activeUsers = createLiveQueryCollection(
  (q) => q
    .from({ user: usersCollection })
    .where(({ user }) => eq(user.active, true))
    .select(({ user }) => ({ id: user.id, name: user.name }))
)

// Full configuration with custom options
const searchResults = createLiveQueryCollection({
  id: "search-results", // Custom ID (auto-generated if omitted)
  query: (q) => q
    .from({ post: postsCollection })
    .where(({ post }) => like(post.title, `%${searchTerm}%`))
    .select(({ post }) => ({
      id: post.id,
      title: post.title,
      excerpt: post.excerpt,
    })),
  getKey: (item) => item.id, // Custom key function (uses stream key if omitted)
  utils: {
    updateSearchTerm: (newTerm: string) => {
      // Custom utility functions
    }
  }
})
```

## Call Signature

```ts
function createLiveQueryCollection<TContext, TResult, TUtils>(config): Collection<TResult, string | number, TUtils>
```

Defined in: [packages/db/src/query/live-query-collection.ts:432](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L432)

Creates a live query collection directly

### Type Parameters

• **TContext** *extends* [`Context`](../../interfaces/context.md)

• **TResult** *extends* `object` = \{ \[K in string \| number \| symbol\]: (TContext\["result"\] extends object ? any\[any\] : TContext\["hasJoins"\] extends true ? TContext\["schema"\] : TContext\["schema"\]\[TContext\["fromSourceName"\]\])\[K\] \}

• **TUtils** *extends* [`UtilsRecord`](../../type-aliases/utilsrecord.md) = \{\}

### Parameters

#### config

[`LiveQueryCollectionConfig`](../../interfaces/livequerycollectionconfig.md)\<`TContext`, `TResult`\> & `object`

### Returns

[`Collection`](../../interfaces/collection.md)\<`TResult`, `string` \| `number`, `TUtils`\>

### Example

```typescript
// Minimal usage - just pass a query function
const activeUsers = createLiveQueryCollection(
  (q) => q
    .from({ user: usersCollection })
    .where(({ user }) => eq(user.active, true))
    .select(({ user }) => ({ id: user.id, name: user.name }))
)

// Full configuration with custom options
const searchResults = createLiveQueryCollection({
  id: "search-results", // Custom ID (auto-generated if omitted)
  query: (q) => q
    .from({ post: postsCollection })
    .where(({ post }) => like(post.title, `%${searchTerm}%`))
    .select(({ post }) => ({
      id: post.id,
      title: post.title,
      excerpt: post.excerpt,
    })),
  getKey: (item) => item.id, // Custom key function (uses stream key if omitted)
  utils: {
    updateSearchTerm: (newTerm: string) => {
      // Custom utility functions
    }
  }
})
```
