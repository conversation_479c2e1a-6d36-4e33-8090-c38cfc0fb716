---
id: max
title: max
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: max()

```ts
function max(arg): Aggregate<number>
```

Defined in: [packages/db/src/query/builder/functions.ts:259](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L259)

## Parameters

### arg

`number` | `BasicExpression`\<`number`\> | `RefProxy`\<`number`\> | `RefProxy`\<`undefined` \| `number`\>

## Returns

`Aggregate`\<`number`\>
