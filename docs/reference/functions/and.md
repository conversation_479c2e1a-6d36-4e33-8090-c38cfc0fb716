---
id: and
title: and
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: and()

## Call Signature

```ts
function and(left, right): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:77](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L77)

### Parameters

#### left

`any`

#### right

`any`

### Returns

`BasicExpression`\<`boolean`\>

## Call Signature

```ts
function and(
   left, 
   right, ...
rest): BasicExpression<boolean>
```

Defined in: [packages/db/src/query/builder/functions.ts:81](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/functions.ts#L81)

### Parameters

#### left

`any`

#### right

`any`

#### rest

...`any`[]

### Returns

`BasicExpression`\<`boolean`\>
