---
id: ElectricInsertHandlerMustReturnTxIdError
title: ElectricInsertHandlerMustReturnTxIdError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: ElectricInsertHandlerMustReturnTxIdError

Defined in: [packages/electric-db-collection/src/errors.ts:25](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/errors.ts#L25)

## Extends

- [`ElectricDBCollectionError`](../electricdbcollectionerror.md)

## Constructors

### new ElectricInsertHandlerMustReturnTxIdError()

```ts
new ElectricInsertHandlerMustReturnTxIdError(): ElectricInsertHandlerMustReturnTxIdError
```

Defined in: [packages/electric-db-collection/src/errors.ts:26](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/errors.ts#L26)

#### Returns

[`ElectricInsertHandlerMustReturnTxIdError`](../electricinserthandlermustreturntxiderror.md)

#### Overrides

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`constructor`](../ElectricDBCollectionError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`cause`](../ElectricDBCollectionError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`message`](../ElectricDBCollectionError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`name`](../ElectricDBCollectionError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`stack`](../ElectricDBCollectionError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`prepareStackTrace`](../ElectricDBCollectionError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`stackTraceLimit`](../ElectricDBCollectionError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`ElectricDBCollectionError`](../electricdbcollectionerror.md).[`captureStackTrace`](../ElectricDBCollectionError.md#capturestacktrace)
