---
id: ElectricDBCollectionError
title: ElectricDBCollectionError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: ElectricDBCollectionError

Defined in: [packages/electric-db-collection/src/errors.ts:4](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/errors.ts#L4)

## Extends

- `TanStackDBError`

## Extended by

- [`ExpectedNumberInAwaitTxIdError`](../expectednumberinawaittxiderror.md)
- [`TimeoutWaitingForTxIdError`](../timeoutwaitingfortxiderror.md)
- [`ElectricInsertHandlerMustReturnTxIdError`](../electricinserthandlermustreturntxiderror.md)
- [`ElectricUpdateHandlerMustReturnTxIdError`](../electricupdatehandlermustreturntxiderror.md)
- [`ElectricDeleteHandlerMustReturnTxIdError`](../electricdeletehandlermustreturntxiderror.md)

## Constructors

### new ElectricDBCollectionError()

```ts
new ElectricDBCollectionError(message): ElectricDBCollectionError
```

Defined in: [packages/electric-db-collection/src/errors.ts:5](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/errors.ts#L5)

#### Parameters

##### message

`string`

#### Returns

[`ElectricDBCollectionError`](../electricdbcollectionerror.md)

#### Overrides

```ts
TanStackDBError.constructor
```

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

```ts
TanStackDBError.cause
```

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

```ts
TanStackDBError.message
```

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

```ts
TanStackDBError.name
```

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

```ts
TanStackDBError.stack
```

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

```ts
TanStackDBError.prepareStackTrace
```

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

```ts
TanStackDBError.stackTraceLimit
```

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

```ts
TanStackDBError.captureStackTrace
```
