---
id: "@tanstack/electric-db-collection"
title: "@tanstack/electric-db-collection"
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# @tanstack/electric-db-collection

## Classes

- [ElectricDBCollectionError](../classes/electricdbcollectionerror.md)
- [ElectricDeleteHandlerMustReturnTxIdError](../classes/electricdeletehandlermustreturntxiderror.md)
- [ElectricInsertHandlerMustReturnTxIdError](../classes/electricinserthandlermustreturntxiderror.md)
- [ElectricUpdateHandlerMustReturnTxIdError](../classes/electricupdatehandlermustreturntxiderror.md)
- [ExpectedNumberInAwaitTxIdError](../classes/expectednumberinawaittxiderror.md)
- [TimeoutWaitingForTxIdError](../classes/timeoutwaitingfortxiderror.md)

## Interfaces

- [ElectricCollectionConfig](../interfaces/electriccollectionconfig.md)
- [ElectricCollectionUtils](../interfaces/electriccollectionutils.md)

## Type Aliases

- [Txid](../type-aliases/txid.md)

## Functions

- [electricCollectionOptions](../functions/electriccollectionoptions.md)
