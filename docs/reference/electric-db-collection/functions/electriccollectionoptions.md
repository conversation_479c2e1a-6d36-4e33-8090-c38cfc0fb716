---
id: electricCollectionOptions
title: electricCollectionOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Function: electricCollectionOptions()

```ts
function electricCollectionOptions<TExplicit, TSchema, TFallback>(config): object
```

Defined in: [packages/electric-db-collection/src/electric.ts:285](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/electric.ts#L285)

Creates Electric collection options for use with a standard Collection

## Type Parameters

• **TExplicit** *extends* `Row`\<`unknown`\> = `Row`\<`unknown`\>

The explicit type of items in the collection (highest priority)

• **TSchema** *extends* `StandardSchemaV1`\<`unknown`, `unknown`\> = `never`

The schema type for validation and type inference (second priority)

• **TFallback** *extends* `Row`\<`unknown`\> = `Row`\<`unknown`\>

The fallback type if no explicit or schema type is provided

## Parameters

### config

[`ElectricCollectionConfig`](../../interfaces/electriccollectionconfig.md)\<`TExplicit`, `TSchema`, `TFallback`\>

Configuration options for the Electric collection

## Returns

`object`

Collection options with utilities

### getKey()

```ts
getKey: (item) => string | number;
```

#### Parameters

##### item

`ResolveType`

#### Returns

`string` \| `number`

### id?

```ts
optional id: string;
```

All standard Collection configuration properties

### onDelete

```ts
onDelete: 
  | undefined
  | (params) => Promise<{
  txid: number | number[];
 }> = wrappedOnDelete;
```

### onInsert

```ts
onInsert: 
  | undefined
  | (params) => Promise<{
  txid: number | number[];
 }> = wrappedOnInsert;
```

### onUpdate

```ts
onUpdate: 
  | undefined
  | (params) => Promise<{
  txid: number | number[];
 }> = wrappedOnUpdate;
```

### schema?

```ts
optional schema: TSchema;
```

### sync

```ts
sync: SyncConfig<ResolveType<TExplicit, TSchema, TFallback>, string | number>;
```

### utils

```ts
utils: object;
```

#### utils.awaitTxId

```ts
awaitTxId: AwaitTxIdFn;
```
