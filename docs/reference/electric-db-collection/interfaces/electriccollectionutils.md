---
id: ElectricCollectionUtils
title: ElectricCollectionUtils
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: ElectricCollectionUtils

Defined in: [packages/electric-db-collection/src/electric.ts:272](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/electric.ts#L272)

Electric collection utilities type

## Extends

- `UtilsRecord`

## Indexable

```ts
[key: string]: Fn
```

## Properties

### awaitTxId

```ts
awaitTxId: AwaitTxIdFn;
```

Defined in: [packages/electric-db-collection/src/electric.ts:273](https://github.com/TanStack/db/blob/main/packages/electric-db-collection/src/electric.ts#L273)
