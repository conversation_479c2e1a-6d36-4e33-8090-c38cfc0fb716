---
id: Context
title: Context
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: Context

Defined in: [packages/db/src/query/builder/types.ts:5](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L5)

## Properties

### baseSchema

```ts
baseSchema: ContextSchema;
```

Defined in: [packages/db/src/query/builder/types.ts:7](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L7)

***

### fromSourceName

```ts
fromSourceName: string;
```

Defined in: [packages/db/src/query/builder/types.ts:11](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L11)

***

### hasJoins?

```ts
optional hasJoins: boolean;
```

Defined in: [packages/db/src/query/builder/types.ts:13](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L13)

***

### joinTypes?

```ts
optional joinTypes: Record<string, "full" | "left" | "right" | "inner" | "outer" | "cross">;
```

Defined in: [packages/db/src/query/builder/types.ts:15](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L15)

***

### result?

```ts
optional result: any;
```

Defined in: [packages/db/src/query/builder/types.ts:20](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L20)

***

### schema

```ts
schema: ContextSchema;
```

Defined in: [packages/db/src/query/builder/types.ts:9](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/types.ts#L9)
