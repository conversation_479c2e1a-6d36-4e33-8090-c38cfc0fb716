---
id: ChangeMessage
title: ChangeMessage
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: ChangeMessage\<T, TKey\>

Defined in: [packages/db/src/types.ts:228](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L228)

## Extended by

- [`OptimisticChangeMessage`](../optimisticchangemessage.md)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

## Properties

### key

```ts
key: TKey;
```

Defined in: [packages/db/src/types.ts:232](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L232)

***

### metadata?

```ts
optional metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:236](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L236)

***

### previousValue?

```ts
optional previousValue: T;
```

Defined in: [packages/db/src/types.ts:234](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L234)

***

### type

```ts
type: OperationType;
```

Defined in: [packages/db/src/types.ts:235](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L235)

***

### value

```ts
value: T;
```

Defined in: [packages/db/src/types.ts:233](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L233)
