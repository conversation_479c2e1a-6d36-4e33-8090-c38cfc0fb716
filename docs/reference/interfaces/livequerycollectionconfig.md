---
id: LiveQueryCollectionConfig
title: LiveQueryCollectionConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: LiveQueryCollectionConfig\<TContext, TResult\>

Defined in: [packages/db/src/query/live-query-collection.ts:47](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L47)

Configuration interface for live query collection options

## Example

```typescript
const config: LiveQueryCollectionConfig<any, any> = {
  // id is optional - will auto-generate "live-query-1", "live-query-2", etc.
  query: (q) => q
    .from({ comment: commentsCollection })
    .join(
      { user: usersCollection },
      ({ comment, user }) => eq(comment.user_id, user.id)
    )
    .where(({ comment }) => eq(comment.active, true))
    .select(({ comment, user }) => ({
      id: comment.id,
      content: comment.content,
      authorName: user.name,
    })),
  // getKey is optional - defaults to using stream key
  getKey: (item) => item.id,
}
```

## Type Parameters

• **TContext** *extends* [`Context`](../context.md)

• **TResult** *extends* `object` = [`GetResult`](../../type-aliases/getresult.md)\<`TContext`\> & `object`

## Properties

### gcTime?

```ts
optional gcTime: number;
```

Defined in: [packages/db/src/query/live-query-collection.ts:90](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L90)

GC time for the collection

***

### getKey()?

```ts
optional getKey: (item) => string | number;
```

Defined in: [packages/db/src/query/live-query-collection.ts:68](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L68)

Function to extract the key from result items
If not provided, defaults to using the key from the D2 stream

#### Parameters

##### item

`TResult`

#### Returns

`string` \| `number`

***

### id?

```ts
optional id: string;
```

Defined in: [packages/db/src/query/live-query-collection.ts:55](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L55)

Unique identifier for the collection
If not provided, defaults to `live-query-${number}` with auto-incrementing number

***

### onDelete?

```ts
optional onDelete: DeleteMutationFn<TResult, string | number, Record<string, Fn>>;
```

Defined in: [packages/db/src/query/live-query-collection.ts:80](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L80)

***

### onInsert?

```ts
optional onInsert: InsertMutationFn<TResult, string | number, Record<string, Fn>>;
```

Defined in: [packages/db/src/query/live-query-collection.ts:78](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L78)

Optional mutation handlers

***

### onUpdate?

```ts
optional onUpdate: UpdateMutationFn<TResult, string | number, Record<string, Fn>>;
```

Defined in: [packages/db/src/query/live-query-collection.ts:79](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L79)

***

### query

```ts
query: 
  | QueryBuilder<TContext>
| (q) => QueryBuilder<TContext>;
```

Defined in: [packages/db/src/query/live-query-collection.ts:60](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L60)

Query builder function that defines the live query

***

### schema?

```ts
optional schema: StandardSchemaV1<unknown, unknown>;
```

Defined in: [packages/db/src/query/live-query-collection.ts:73](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L73)

Optional schema for validation

***

### startSync?

```ts
optional startSync: boolean;
```

Defined in: [packages/db/src/query/live-query-collection.ts:85](https://github.com/TanStack/db/blob/main/packages/db/src/query/live-query-collection.ts#L85)

Start sync / the query immediately
