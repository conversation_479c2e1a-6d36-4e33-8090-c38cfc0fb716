---
id: IndexOptions
title: IndexOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: IndexOptions\<TResolver\>

Defined in: [packages/db/src/indexes/index-options.ts:6](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/index-options.ts#L6)

Enhanced index options that support both sync and async resolvers

## Type Parameters

• **TResolver** *extends* [`IndexResolver`](../../type-aliases/indexresolver.md) = [`IndexResolver`](../../type-aliases/indexresolver.md)

## Properties

### indexType?

```ts
optional indexType: TResolver;
```

Defined in: [packages/db/src/indexes/index-options.ts:8](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/index-options.ts#L8)

***

### name?

```ts
optional name: string;
```

Defined in: [packages/db/src/indexes/index-options.ts:7](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/index-options.ts#L7)

***

### options?

```ts
optional options: TResolver extends IndexConstructor<any> ? TResolver<TResolver> extends (id, expr, name?, options?) => any ? O : never : TResolver extends () => Promise<TCtor> ? TCtor extends (id, expr, name?, options?) => any ? O : never : never;
```

Defined in: [packages/db/src/indexes/index-options.ts:9](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/index-options.ts#L9)
