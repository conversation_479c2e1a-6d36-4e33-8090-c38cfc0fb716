---
id: CurrentStateAsChangesOptions
title: CurrentStateAsChangesOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: CurrentStateAsChangesOptions\<T\>

Defined in: [packages/db/src/types.ts:587](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L587)

Options for getting current state as changes

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Properties

### where()?

```ts
optional where: (row) => any;
```

Defined in: [packages/db/src/types.ts:591](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L591)

Filter the current state using a where expression

#### Parameters

##### row

`SingleRowRefProxy`\<`T`\>

#### Returns

`any`

***

### whereExpression?

```ts
optional whereExpression: BasicExpression<boolean>;
```

Defined in: [packages/db/src/types.ts:593](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L593)

Pre-compiled expression for filtering the current state
