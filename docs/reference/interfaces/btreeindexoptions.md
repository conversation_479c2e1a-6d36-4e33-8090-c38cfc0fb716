---
id: BTreeIndexOptions
title: BTreeIndexOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: BTreeIndexOptions

Defined in: [packages/db/src/indexes/btree-index.ts:10](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L10)

Options for Ordered index

## Properties

### compareFn()?

```ts
optional compareFn: (a, b) => number;
```

Defined in: [packages/db/src/indexes/btree-index.ts:11](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L11)

#### Parameters

##### a

`any`

##### b

`any`

#### Returns

`number`
