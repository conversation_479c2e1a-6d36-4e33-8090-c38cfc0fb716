---
id: PendingMutation
title: PendingMutation
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: PendingMutation\<T, TOperation, TCollection\>

Defined in: [packages/db/src/types.ts:103](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L103)

Represents a pending mutation within a transaction
Contains information about the original and modified data, as well as metadata

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TOperation** *extends* [`OperationType`](../../type-aliases/operationtype.md) = [`OperationType`](../../type-aliases/operationtype.md)

• **TCollection** *extends* [`Collection`](../collection.md)\<`T`, `any`, `any`, `any`, `any`\> = [`Collection`](../collection.md)\<`T`, `any`, `any`, `any`, `any`\>

## Properties

### changes

```ts
changes: ResolveTransactionChanges<T, TOperation>;
```

Defined in: [packages/db/src/types.ts:120](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L120)

***

### collection

```ts
collection: TCollection;
```

Defined in: [packages/db/src/types.ts:131](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L131)

***

### createdAt

```ts
createdAt: Date;
```

Defined in: [packages/db/src/types.ts:129](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L129)

***

### globalKey

```ts
globalKey: string;
```

Defined in: [packages/db/src/types.ts:121](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L121)

***

### key

```ts
key: any;
```

Defined in: [packages/db/src/types.ts:123](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L123)

***

### metadata

```ts
metadata: unknown;
```

Defined in: [packages/db/src/types.ts:125](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L125)

***

### modified

```ts
modified: T;
```

Defined in: [packages/db/src/types.ts:118](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L118)

***

### mutationId

```ts
mutationId: string;
```

Defined in: [packages/db/src/types.ts:114](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L114)

***

### optimistic

```ts
optimistic: boolean;
```

Defined in: [packages/db/src/types.ts:128](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L128)

Whether this mutation should be applied optimistically (defaults to true)

***

### original

```ts
original: TOperation extends "insert" ? object : T;
```

Defined in: [packages/db/src/types.ts:116](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L116)

***

### syncMetadata

```ts
syncMetadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:126](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L126)

***

### type

```ts
type: TOperation;
```

Defined in: [packages/db/src/types.ts:124](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L124)

***

### updatedAt

```ts
updatedAt: Date;
```

Defined in: [packages/db/src/types.ts:130](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L130)
