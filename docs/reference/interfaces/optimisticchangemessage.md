---
id: OptimisticChangeMessage
title: OptimisticChangeMessage
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: OptimisticChangeMessage\<T\>

Defined in: [packages/db/src/types.ts:239](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L239)

## Extends

- [`ChangeMessage`](../changemessage.md)\<`T`\>

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Properties

### isActive?

```ts
optional isActive: boolean;
```

Defined in: [packages/db/src/types.ts:243](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L243)

***

### key

```ts
key: string | number;
```

Defined in: [packages/db/src/types.ts:232](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L232)

#### Inherited from

[`ChangeMessage`](../changemessage.md).[`key`](../ChangeMessage.md#key)

***

### metadata?

```ts
optional metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:236](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L236)

#### Inherited from

[`ChangeMessage`](../changemessage.md).[`metadata`](../ChangeMessage.md#metadata)

***

### previousValue?

```ts
optional previousValue: T;
```

Defined in: [packages/db/src/types.ts:234](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L234)

#### Inherited from

[`ChangeMessage`](../changemessage.md).[`previousValue`](../ChangeMessage.md#previousvalue)

***

### type

```ts
type: OperationType;
```

Defined in: [packages/db/src/types.ts:235](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L235)

#### Inherited from

[`ChangeMessage`](../changemessage.md).[`type`](../ChangeMessage.md#type)

***

### value

```ts
value: T;
```

Defined in: [packages/db/src/types.ts:233](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L233)

#### Inherited from

[`ChangeMessage`](../changemessage.md).[`value`](../ChangeMessage.md#value)
