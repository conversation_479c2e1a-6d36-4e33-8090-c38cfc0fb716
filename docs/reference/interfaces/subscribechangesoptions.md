---
id: SubscribeChangesOptions
title: SubscribeChangesOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: SubscribeChangesOptions\<T\>

Defined in: [packages/db/src/types.ts:573](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L573)

Options for subscribing to collection changes

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Properties

### includeInitialState?

```ts
optional includeInitialState: boolean;
```

Defined in: [packages/db/src/types.ts:577](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L577)

Whether to include the current state as initial changes

***

### where()?

```ts
optional where: (row) => any;
```

Defined in: [packages/db/src/types.ts:579](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L579)

Filter changes using a where expression

#### Parameters

##### row

`SingleRowRefProxy`\<`T`\>

#### Returns

`any`

***

### whereExpression?

```ts
optional whereExpression: BasicExpression<boolean>;
```

Defined in: [packages/db/src/types.ts:581](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L581)

Pre-compiled expression for filtering changes
