---
id: InsertConfig
title: InsertConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: InsertConfig

Defined in: [packages/db/src/types.ts:270](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L270)

## Properties

### metadata?

```ts
optional metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:271](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L271)

***

### optimistic?

```ts
optional optimistic: boolean;
```

Defined in: [packages/db/src/types.ts:273](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L273)

Whether to apply optimistic updates immediately. Defaults to true.
