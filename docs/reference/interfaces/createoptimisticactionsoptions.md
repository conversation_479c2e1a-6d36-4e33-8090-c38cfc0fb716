---
id: CreateOptimisticActionsOptions
title: CreateOptimisticActionsOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: CreateOptimisticActionsOptions\<TVars, T\>

Defined in: [packages/db/src/types.ts:174](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L174)

Options for the createOptimisticAction helper

## Extends

- `Omit`\<[`TransactionConfig`](../transactionconfig.md)\<`T`\>, `"mutationFn"`\>

## Type Parameters

• **TVars** = `unknown`

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Properties

### autoCommit?

```ts
optional autoCommit: boolean;
```

Defined in: [packages/db/src/types.ts:165](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L165)

#### Inherited from

```ts
Omit.autoCommit
```

***

### id?

```ts
optional id: string;
```

Defined in: [packages/db/src/types.ts:163](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L163)

Unique identifier for the transaction

#### Inherited from

```ts
Omit.id
```

***

### metadata?

```ts
optional metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:168](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L168)

Custom metadata to associate with the transaction

#### Inherited from

```ts
Omit.metadata
```

***

### mutationFn()

```ts
mutationFn: (vars, params) => Promise<any>;
```

Defined in: [packages/db/src/types.ts:181](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L181)

Function to execute the mutation on the server

#### Parameters

##### vars

`TVars`

##### params

[`MutationFnParams`](../../type-aliases/mutationfnparams.md)\<`T`\>

#### Returns

`Promise`\<`any`\>

***

### onMutate()

```ts
onMutate: (vars) => void;
```

Defined in: [packages/db/src/types.ts:179](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L179)

Function to apply optimistic updates locally before the mutation completes

#### Parameters

##### vars

`TVars`

#### Returns

`void`
