---
id: OperationConfig
title: OperationConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: OperationConfig

Defined in: [packages/db/src/types.ts:264](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L264)

## Properties

### metadata?

```ts
optional metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:265](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L265)

***

### optimistic?

```ts
optional optimistic: boolean;
```

Defined in: [packages/db/src/types.ts:267](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L267)

Whether to apply optimistic updates immediately. Defaults to true.
