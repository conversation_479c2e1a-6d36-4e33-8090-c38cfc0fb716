---
id: LocalOnlyCollectionConfig
title: LocalOnlyCollectionConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: LocalOnlyCollectionConfig\<TExplicit, TSchema, T<PERSON><PERSON>back, TK<PERSON>\>

Defined in: [packages/db/src/local-only.ts:27](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L27)

Configuration interface for Local-only collection options

## Remarks

Type resolution follows a priority order:
1. If you provide an explicit type via generic parameter, it will be used
2. If no explicit type is provided but a schema is, the schema's output type will be inferred
3. If neither explicit type nor schema is provided, the fallback type will be used

You should provide EITHER an explicit type OR a schema, but not both, as they would conflict.

## Type Parameters

• **TExplicit** = `unknown`

The explicit type of items in the collection (highest priority)

• **TSchema** *extends* `StandardSchemaV1` = `never`

The schema type for validation and type inference (second priority)

• **TFallback** *extends* `Record`\<`string`, `unknown`\> = `Record`\<`string`, `unknown`\>

The fallback type if no explicit or schema type is provided

• **TKey** *extends* `string` \| `number` = `string` \| `number`

The type of the key returned by getKey

## Properties

### getKey()

```ts
getKey: (item) => TKey;
```

Defined in: [packages/db/src/local-only.ts:38](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L38)

#### Parameters

##### item

[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>

#### Returns

`TKey`

***

### id?

```ts
optional id: string;
```

Defined in: [packages/db/src/local-only.ts:36](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L36)

Standard Collection configuration properties

***

### initialData?

```ts
optional initialData: ResolveType<TExplicit, TSchema, TFallback>[];
```

Defined in: [packages/db/src/local-only.ts:44](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L44)

Optional initial data to populate the collection with on creation
This data will be applied during the initial sync process

***

### onDelete()?

```ts
optional onDelete: (params) => Promise<any>;
```

Defined in: [packages/db/src/local-only.ts:77](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L77)

Optional asynchronous handler function called after a delete operation

#### Parameters

##### params

[`DeleteMutationFnParams`](../../type-aliases/deletemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, [`LocalOnlyCollectionUtils`](../localonlycollectionutils.md)\>

Object containing transaction and collection information

#### Returns

`Promise`\<`any`\>

Promise resolving to any value

***

### onInsert()?

```ts
optional onInsert: (params) => Promise<any>;
```

Defined in: [packages/db/src/local-only.ts:51](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L51)

Optional asynchronous handler function called after an insert operation

#### Parameters

##### params

[`InsertMutationFnParams`](../../type-aliases/insertmutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, [`LocalOnlyCollectionUtils`](../localonlycollectionutils.md)\>

Object containing transaction and collection information

#### Returns

`Promise`\<`any`\>

Promise resolving to any value

***

### onUpdate()?

```ts
optional onUpdate: (params) => Promise<any>;
```

Defined in: [packages/db/src/local-only.ts:64](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L64)

Optional asynchronous handler function called after an update operation

#### Parameters

##### params

[`UpdateMutationFnParams`](../../type-aliases/updatemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>, `TKey`, [`LocalOnlyCollectionUtils`](../localonlycollectionutils.md)\>

Object containing transaction and collection information

#### Returns

`Promise`\<`any`\>

Promise resolving to any value

***

### schema?

```ts
optional schema: TSchema;
```

Defined in: [packages/db/src/local-only.ts:37](https://github.com/TanStack/db/blob/main/packages/db/src/local-only.ts#L37)
