---
id: SyncConfig
title: SyncConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: SyncConfig\<T, TKey\>

Defined in: [packages/db/src/types.ts:200](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L200)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

• **TKey** *extends* `string` \| `number` = `string` \| `number`

## Properties

### getSyncMetadata()?

```ts
optional getSyncMetadata: () => Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:216](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L216)

Get the sync metadata for insert operations

#### Returns

`Record`\<`string`, `unknown`\>

Record containing relation information

***

### rowUpdateMode?

```ts
optional rowUpdateMode: "partial" | "full";
```

Defined in: [packages/db/src/types.ts:225](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L225)

The row update mode used to sync to the collection.

#### Default

`partial`

#### Description

- `partial`: Updates contain only the changes to the row.
- `full`: Updates contain the entire row.

***

### sync()

```ts
sync: (params) => void;
```

Defined in: [packages/db/src/types.ts:204](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L204)

#### Parameters

##### params

###### begin

() => `void`

###### collection

[`Collection`](../collection.md)\<`T`, `TKey`, `any`, `any`, `any`\>

###### commit

() => `void`

###### markReady

() => `void`

###### write

(`message`) => `void`

#### Returns

`void`
