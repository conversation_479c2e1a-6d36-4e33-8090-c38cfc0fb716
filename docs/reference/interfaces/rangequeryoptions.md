---
id: RangeQueryOptions
title: RangeQueryOptions
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: RangeQueryOptions

Defined in: [packages/db/src/indexes/btree-index.ts:17](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L17)

Options for range queries

## Properties

### from?

```ts
optional from: any;
```

Defined in: [packages/db/src/indexes/btree-index.ts:18](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L18)

***

### fromInclusive?

```ts
optional fromInclusive: boolean;
```

Defined in: [packages/db/src/indexes/btree-index.ts:20](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L20)

***

### to?

```ts
optional to: any;
```

Defined in: [packages/db/src/indexes/btree-index.ts:19](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L19)

***

### toInclusive?

```ts
optional toInclusive: boolean;
```

Defined in: [packages/db/src/indexes/btree-index.ts:21](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L21)
