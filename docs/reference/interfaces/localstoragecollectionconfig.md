---
id: LocalStorageCollectionConfig
title: LocalStorageCollectionConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: LocalStorageCollectionConfig\<TExplicit, TSchema, TFallback\>

Defined in: [packages/db/src/local-storage.ts:61](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L61)

Configuration interface for localStorage collection options

## Remarks

Type resolution follows a priority order:
1. If you provide an explicit type via generic parameter, it will be used
2. If no explicit type is provided but a schema is, the schema's output type will be inferred
3. If neither explicit type nor schema is provided, the fallback type will be used

You should provide EITHER an explicit type OR a schema, but not both, as they would conflict.

## Type Parameters

• **TExplicit** = `unknown`

The explicit type of items in the collection (highest priority)

• **TSchema** *extends* `StandardSchemaV1` = `never`

The schema type for validation and type inference (second priority)

• **TFallback** *extends* `object` = `Record`\<`string`, `unknown`\>

The fallback type if no explicit or schema type is provided

## Properties

### getKey()

```ts
getKey: (item) => string | number;
```

Defined in: [packages/db/src/local-storage.ts:88](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L88)

#### Parameters

##### item

[`ResolveType`](../../type-aliases/resolvetype.md)

#### Returns

`string` \| `number`

***

### id?

```ts
optional id: string;
```

Defined in: [packages/db/src/local-storage.ts:86](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L86)

Collection identifier (defaults to "local-collection:{storageKey}" if not provided)

***

### onDelete()?

```ts
optional onDelete: (params) => Promise<any>;
```

Defined in: [packages/db/src/local-storage.ts:114](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L114)

Optional asynchronous handler function called before a delete operation

#### Parameters

##### params

[`DeleteMutationFnParams`](../../type-aliases/deletemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

Object containing transaction and collection information

#### Returns

`Promise`\<`any`\>

Promise resolving to any value

***

### onInsert()?

```ts
optional onInsert: (params) => Promise<any>;
```

Defined in: [packages/db/src/local-storage.ts:96](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L96)

Optional asynchronous handler function called before an insert operation

#### Parameters

##### params

[`InsertMutationFnParams`](../../type-aliases/insertmutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

Object containing transaction and collection information

#### Returns

`Promise`\<`any`\>

Promise resolving to any value

***

### onUpdate()?

```ts
optional onUpdate: (params) => Promise<any>;
```

Defined in: [packages/db/src/local-storage.ts:105](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L105)

Optional asynchronous handler function called before an update operation

#### Parameters

##### params

[`UpdateMutationFnParams`](../../type-aliases/updatemutationfnparams.md)\<[`ResolveType`](../../type-aliases/resolvetype.md)\<`TExplicit`, `TSchema`, `TFallback`\>\>

Object containing transaction and collection information

#### Returns

`Promise`\<`any`\>

Promise resolving to any value

***

### schema?

```ts
optional schema: TSchema;
```

Defined in: [packages/db/src/local-storage.ts:87](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L87)

***

### storage?

```ts
optional storage: StorageApi;
```

Defined in: [packages/db/src/local-storage.ts:75](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L75)

Storage API to use (defaults to window.localStorage)
Can be any object that implements the Storage interface (e.g., sessionStorage)

***

### storageEventApi?

```ts
optional storageEventApi: StorageEventApi;
```

Defined in: [packages/db/src/local-storage.ts:81](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L81)

Storage event API to use for cross-tab synchronization (defaults to window)
Can be any object that implements addEventListener/removeEventListener for storage events

***

### storageKey

```ts
storageKey: string;
```

Defined in: [packages/db/src/local-storage.ts:69](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L69)

The key to use for storing the collection data in localStorage/sessionStorage

***

### sync?

```ts
optional sync: SyncConfig<ResolveType<TExplicit, TSchema, TFallback>, string | number>;
```

Defined in: [packages/db/src/local-storage.ts:89](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L89)
