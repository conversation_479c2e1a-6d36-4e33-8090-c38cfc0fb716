---
id: LocalStorageCollectionUtils
title: LocalStorageCollectionUtils
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: LocalStorageCollectionUtils

Defined in: [packages/db/src/local-storage.ts:132](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L132)

LocalStorage collection utilities type

## Extends

- [`UtilsRecord`](../../type-aliases/utilsrecord.md)

## Indexable

```ts
[key: string]: Fn
```

## Properties

### clearStorage

```ts
clearStorage: ClearStorageFn;
```

Defined in: [packages/db/src/local-storage.ts:133](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L133)

***

### getStorageSize

```ts
getStorageSize: GetStorageSizeFn;
```

Defined in: [packages/db/src/local-storage.ts:134](https://github.com/TanStack/db/blob/main/packages/db/src/local-storage.ts#L134)
