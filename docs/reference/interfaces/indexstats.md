---
id: IndexStats
title: IndexStats
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: IndexStats

Defined in: [packages/db/src/indexes/base-index.ts:18](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L18)

Statistics about index usage and performance

## Properties

### averageLookupTime

```ts
readonly averageLookupTime: number;
```

Defined in: [packages/db/src/indexes/base-index.ts:21](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L21)

***

### entryCount

```ts
readonly entryCount: number;
```

Defined in: [packages/db/src/indexes/base-index.ts:19](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L19)

***

### lastUpdated

```ts
readonly lastUpdated: Date;
```

Defined in: [packages/db/src/indexes/base-index.ts:22](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L22)

***

### lookupCount

```ts
readonly lookupCount: number;
```

Defined in: [packages/db/src/indexes/base-index.ts:20](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L20)
