---
id: TransactionConfig
title: TransactionConfig
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Interface: TransactionConfig\<T\>

Defined in: [packages/db/src/types.ts:161](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L161)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Properties

### autoCommit?

```ts
optional autoCommit: boolean;
```

Defined in: [packages/db/src/types.ts:165](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L165)

***

### id?

```ts
optional id: string;
```

Defined in: [packages/db/src/types.ts:163](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L163)

Unique identifier for the transaction

***

### metadata?

```ts
optional metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/types.ts:168](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L168)

Custom metadata to associate with the transaction

***

### mutationFn

```ts
mutationFn: MutationFn<T>;
```

Defined in: [packages/db/src/types.ts:166](https://github.com/TanStack/db/blob/main/packages/db/src/types.ts#L166)
