---
id: IndexProxy
title: IndexProxy
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: IndexProxy\<TKey\>

Defined in: [packages/db/src/indexes/lazy-index.ts:131](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L131)

Proxy that provides synchronous interface while index loads asynchronously

## Type Parameters

• **TKey** *extends* `string` \| `number` = `string` \| `number`

## Constructors

### new IndexProxy()

```ts
new IndexProxy<TKey>(indexId, lazyIndex): IndexProxy<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:132](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L132)

#### Parameters

##### indexId

`number`

##### lazyIndex

[`LazyIndexWrapper`](../lazyindexwrapper.md)\<`TKey`\>

#### Returns

[`IndexProxy`](../indexproxy.md)\<`TKey`\>

## Accessors

### expression

#### Get Signature

```ts
get expression(): BasicExpression
```

Defined in: [packages/db/src/indexes/lazy-index.ts:178](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L178)

Get the index expression (available immediately)

##### Returns

`BasicExpression`

***

### id

#### Get Signature

```ts
get id(): number
```

Defined in: [packages/db/src/indexes/lazy-index.ts:161](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L161)

Get the index ID

##### Returns

`number`

***

### index

#### Get Signature

```ts
get index(): BaseIndex<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:140](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L140)

Get the resolved index (throws if not ready)

##### Returns

[`BaseIndex`](../baseindex.md)\<`TKey`\>

***

### indexedKeysSet

#### Get Signature

```ts
get indexedKeysSet(): Set<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:216](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L216)

##### Returns

`Set`\<`TKey`\>

***

### isReady

#### Get Signature

```ts
get isReady(): boolean
```

Defined in: [packages/db/src/indexes/lazy-index.ts:147](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L147)

Check if index is ready

##### Returns

`boolean`

***

### keyCount

#### Get Signature

```ts
get keyCount(): number
```

Defined in: [packages/db/src/indexes/lazy-index.ts:211](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L211)

Get the key count (throws if not ready)

##### Returns

`number`

***

### name

#### Get Signature

```ts
get name(): undefined | string
```

Defined in: [packages/db/src/indexes/lazy-index.ts:168](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L168)

Get the index name (throws if not ready)

##### Returns

`undefined` \| `string`

***

### orderedEntriesArray

#### Get Signature

```ts
get orderedEntriesArray(): [any, Set<TKey>][]
```

Defined in: [packages/db/src/indexes/lazy-index.ts:221](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L221)

##### Returns

\[`any`, `Set`\<`TKey`\>\][]

***

### valueMapData

#### Get Signature

```ts
get valueMapData(): Map<any, Set<TKey>>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:226](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L226)

##### Returns

`Map`\<`any`, `Set`\<`TKey`\>\>

## Methods

### \_getLazyWrapper()

```ts
_getLazyWrapper(): LazyIndexWrapper<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:248](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L248)

#### Returns

[`LazyIndexWrapper`](../lazyindexwrapper.md)\<`TKey`\>

***

### equalityLookup()

```ts
equalityLookup(value): Set<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:232](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L232)

#### Parameters

##### value

`any`

#### Returns

`Set`\<`TKey`\>

***

### getStats()

```ts
getStats(): IndexStats
```

Defined in: [packages/db/src/indexes/lazy-index.ts:192](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L192)

Get index statistics (throws if not ready)

#### Returns

[`IndexStats`](../../interfaces/indexstats.md)

***

### inArrayLookup()

```ts
inArrayLookup(values): Set<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:242](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L242)

#### Parameters

##### values

`any`[]

#### Returns

`Set`\<`TKey`\>

***

### matchesField()

```ts
matchesField(fieldPath): boolean
```

Defined in: [packages/db/src/indexes/lazy-index.ts:199](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L199)

Check if index matches a field path (available immediately)

#### Parameters

##### fieldPath

`string`[]

#### Returns

`boolean`

***

### rangeQuery()

```ts
rangeQuery(options): Set<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:237](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L237)

#### Parameters

##### options

`any`

#### Returns

`Set`\<`TKey`\>

***

### supports()

```ts
supports(operation): boolean
```

Defined in: [packages/db/src/indexes/lazy-index.ts:185](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L185)

Check if index supports an operation (throws if not ready)

#### Parameters

##### operation

`any`

#### Returns

`boolean`

***

### whenReady()

```ts
whenReady(): Promise<BaseIndex<TKey>>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:154](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L154)

Wait for index to be ready

#### Returns

`Promise`\<[`BaseIndex`](../baseindex.md)\<`TKey`\>\>
