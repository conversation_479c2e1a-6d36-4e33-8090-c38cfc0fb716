---
id: TransactionNotPendingCommitError
title: TransactionNotPendingCommitError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: TransactionNotPendingCommitError

Defined in: [packages/db/src/errors.ts:248](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L248)

## Extends

- [`TransactionError`](../transactionerror.md)

## Constructors

### new TransactionNotPendingCommitError()

```ts
new TransactionNotPendingCommitError(): TransactionNotPendingCommitError
```

Defined in: [packages/db/src/errors.ts:249](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L249)

#### Returns

[`TransactionNotPendingCommitError`](../transactionnotpendingcommiterror.md)

#### Overrides

[`TransactionError`](../transactionerror.md).[`constructor`](../TransactionError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`TransactionError`](../transactionerror.md).[`cause`](../TransactionError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`TransactionError`](../transactionerror.md).[`message`](../TransactionError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`TransactionError`](../transactionerror.md).[`name`](../TransactionError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`TransactionError`](../transactionerror.md).[`stack`](../TransactionError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`TransactionError`](../transactionerror.md).[`prepareStackTrace`](../TransactionError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`TransactionError`](../transactionerror.md).[`stackTraceLimit`](../TransactionError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`TransactionError`](../transactionerror.md).[`captureStackTrace`](../TransactionError.md#capturestacktrace)
