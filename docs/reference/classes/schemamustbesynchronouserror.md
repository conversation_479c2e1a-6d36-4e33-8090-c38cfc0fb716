---
id: SchemaMustBeSynchronousError
title: SchemaMustBeSynchronousError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: SchemaMustBeSynchronousError

Defined in: [packages/db/src/errors.ts:70](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L70)

## Extends

- [`CollectionConfigurationError`](../collectionconfigurationerror.md)

## Constructors

### new SchemaMustBeSynchronousError()

```ts
new SchemaMustBeSynchronousError(): SchemaMustBeSynchronousError
```

Defined in: [packages/db/src/errors.ts:71](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L71)

#### Returns

[`SchemaMustBeSynchronousError`](../schemamustbesynchronouserror.md)

#### Overrides

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`constructor`](../CollectionConfigurationError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`cause`](../CollectionConfigurationError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`message`](../CollectionConfigurationError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`name`](../CollectionConfigurationError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`stack`](../CollectionConfigurationError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`prepareStackTrace`](../CollectionConfigurationError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`stackTraceLimit`](../CollectionConfigurationError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`CollectionConfigurationError`](../collectionconfigurationerror.md).[`captureStackTrace`](../CollectionConfigurationError.md#capturestacktrace)
