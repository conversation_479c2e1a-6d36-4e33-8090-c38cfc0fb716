---
id: Update<PERSON>eyNotFoundError
title: UpdateKeyNotFoundError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: UpdateKeyNotFoundError

Defined in: [packages/db/src/errors.ts:156](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L156)

## Extends

- [`CollectionOperationError`](../collectionoperationerror.md)

## Constructors

### new UpdateKeyNotFoundError()

```ts
new UpdateKeyNotFoundError(key): UpdateKeyNotFoundError
```

Defined in: [packages/db/src/errors.ts:157](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L157)

#### Parameters

##### key

`string` | `number`

#### Returns

[`UpdateKeyNotFoundError`](../updatekeynotfounderror.md)

#### Overrides

[`CollectionOperationError`](../collectionoperationerror.md).[`constructor`](../CollectionOperationError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`cause`](../CollectionOperationError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`message`](../CollectionOperationError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`name`](../CollectionOperationError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`stack`](../CollectionOperationError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`prepareStackTrace`](../CollectionOperationError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`stackTraceLimit`](../CollectionOperationError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`CollectionOperationError`](../collectionoperationerror.md).[`captureStackTrace`](../CollectionOperationError.md#capturestacktrace)
