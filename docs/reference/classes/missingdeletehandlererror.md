---
id: MissingDeleteHandlerError
title: MissingDeleteHandlerError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: MissingDeleteHandlerError

Defined in: [packages/db/src/errors.ts:210](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L210)

## Extends

- [`MissingHandlerError`](../missinghandlererror.md)

## Constructors

### new MissingDeleteHandlerError()

```ts
new MissingDeleteHandlerError(): MissingDeleteHandlerError
```

Defined in: [packages/db/src/errors.ts:211](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L211)

#### Returns

[`MissingDeleteHandlerError`](../missingdeletehandlererror.md)

#### Overrides

[`MissingHandlerError`](../missinghandlererror.md).[`constructor`](../MissingHandlerError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`cause`](../MissingHandlerError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`message`](../MissingHandlerError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`name`](../MissingHandlerError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`stack`](../MissingHandlerError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`prepareStackTrace`](../MissingHandlerError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`stackTraceLimit`](../MissingHandlerError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`MissingHandlerError`](../missinghandlererror.md).[`captureStackTrace`](../MissingHandlerError.md#capturestacktrace)
