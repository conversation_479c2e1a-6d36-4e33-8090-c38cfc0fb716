---
id: SerializationError
title: SerializationError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: SerializationError

Defined in: [packages/db/src/errors.ts:473](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L473)

## Extends

- [`StorageError`](../storageerror.md)

## Constructors

### new SerializationError()

```ts
new SerializationError(operation, originalError): SerializationError
```

Defined in: [packages/db/src/errors.ts:474](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L474)

#### Parameters

##### operation

`string`

##### originalError

`string`

#### Returns

[`SerializationError`](../serializationerror.md)

#### Overrides

[`StorageError`](../storageerror.md).[`constructor`](../StorageError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`StorageError`](../storageerror.md).[`cause`](../StorageError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`StorageError`](../storageerror.md).[`message`](../StorageError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`StorageError`](../storageerror.md).[`name`](../StorageError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`StorageError`](../storageerror.md).[`stack`](../StorageError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`StorageError`](../storageerror.md).[`prepareStackTrace`](../StorageError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`StorageError`](../storageerror.md).[`stackTraceLimit`](../StorageError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`StorageError`](../storageerror.md).[`captureStackTrace`](../StorageError.md#capturestacktrace)
