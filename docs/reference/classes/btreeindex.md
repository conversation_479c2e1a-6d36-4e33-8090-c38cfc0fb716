---
id: BTreeIndex
title: BTreeIndex
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: BTreeIndex\<TKey\>

Defined in: [packages/db/src/indexes/btree-index.ts:28](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L28)

B+Tree index for sorted data with range queries
This maintains items in sorted order and provides efficient range operations

## Extends

- [`BaseIndex`](../baseindex.md)\<`TKey`\>

## Type Parameters

• **TKey** *extends* `string` \| `number` = `string` \| `number`

## Constructors

### new BTreeIndex()

```ts
new BTreeIndex<TKey>(
   id, 
   expression, 
   name?, 
options?): BTreeIndex<TKey>
```

Defined in: [packages/db/src/indexes/btree-index.ts:48](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L48)

#### Parameters

##### id

`number`

##### expression

`BasicExpression`

##### name?

`string`

##### options?

`any`

#### Returns

[`BTreeIndex`](../btreeindex.md)\<`TKey`\>

#### Overrides

[`BaseIndex`](../baseindex.md).[`constructor`](../BaseIndex.md#constructors)

## Properties

### expression

```ts
readonly expression: BasicExpression;
```

Defined in: [packages/db/src/indexes/base-index.ts:33](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L33)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`expression`](../BaseIndex.md#expression-1)

***

### id

```ts
readonly id: number;
```

Defined in: [packages/db/src/indexes/base-index.ts:31](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L31)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`id`](../BaseIndex.md#id-1)

***

### lastUpdated

```ts
protected lastUpdated: Date;
```

Defined in: [packages/db/src/indexes/base-index.ts:38](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L38)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`lastUpdated`](../BaseIndex.md#lastupdated)

***

### lookupCount

```ts
protected lookupCount: number = 0;
```

Defined in: [packages/db/src/indexes/base-index.ts:36](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L36)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`lookupCount`](../BaseIndex.md#lookupcount)

***

### name?

```ts
readonly optional name: string;
```

Defined in: [packages/db/src/indexes/base-index.ts:32](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L32)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`name`](../BaseIndex.md#name-1)

***

### supportedOperations

```ts
readonly supportedOperations: Set<"eq" | "gt" | "gte" | "lt" | "lte" | "in" | "like" | "ilike">;
```

Defined in: [packages/db/src/indexes/btree-index.ts:31](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L31)

#### Overrides

[`BaseIndex`](../baseindex.md).[`supportedOperations`](../BaseIndex.md#supportedoperations)

***

### totalLookupTime

```ts
protected totalLookupTime: number = 0;
```

Defined in: [packages/db/src/indexes/base-index.ts:37](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L37)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`totalLookupTime`](../BaseIndex.md#totallookuptime)

## Accessors

### indexedKeysSet

#### Get Signature

```ts
get indexedKeysSet(): Set<TKey>
```

Defined in: [packages/db/src/indexes/btree-index.ts:250](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L250)

##### Returns

`Set`\<`TKey`\>

***

### keyCount

#### Get Signature

```ts
get keyCount(): number
```

Defined in: [packages/db/src/indexes/btree-index.ts:188](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L188)

Gets the number of indexed keys

##### Returns

`number`

#### Overrides

[`BaseIndex`](../baseindex.md).[`keyCount`](../BaseIndex.md#keycount)

***

### orderedEntriesArray

#### Get Signature

```ts
get orderedEntriesArray(): [any, Set<TKey>][]
```

Defined in: [packages/db/src/indexes/btree-index.ts:254](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L254)

##### Returns

\[`any`, `Set`\<`TKey`\>\][]

***

### valueMapData

#### Get Signature

```ts
get valueMapData(): Map<any, Set<TKey>>
```

Defined in: [packages/db/src/indexes/btree-index.ts:260](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L260)

##### Returns

`Map`\<`any`, `Set`\<`TKey`\>\>

## Methods

### add()

```ts
add(key, item): void
```

Defined in: [packages/db/src/indexes/btree-index.ts:64](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L64)

Adds a value to the index

#### Parameters

##### key

`TKey`

##### item

`any`

#### Returns

`void`

#### Overrides

[`BaseIndex`](../baseindex.md).[`add`](../BaseIndex.md#add)

***

### build()

```ts
build(entries): void
```

Defined in: [packages/db/src/indexes/btree-index.ts:132](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L132)

Builds the index from a collection of entries

#### Parameters

##### entries

`Iterable`\<\[`TKey`, `any`\]\>

#### Returns

`void`

#### Overrides

[`BaseIndex`](../baseindex.md).[`build`](../BaseIndex.md#build)

***

### clear()

```ts
clear(): void
```

Defined in: [packages/db/src/indexes/btree-index.ts:143](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L143)

Clears all data from the index

#### Returns

`void`

#### Overrides

[`BaseIndex`](../baseindex.md).[`clear`](../BaseIndex.md#clear)

***

### equalityLookup()

```ts
equalityLookup(value): Set<TKey>
```

Defined in: [packages/db/src/indexes/btree-index.ts:197](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L197)

Performs an equality lookup

#### Parameters

##### value

`any`

#### Returns

`Set`\<`TKey`\>

***

### evaluateIndexExpression()

```ts
protected evaluateIndexExpression(item): any
```

Defined in: [packages/db/src/indexes/base-index.ts:87](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L87)

#### Parameters

##### item

`any`

#### Returns

`any`

#### Inherited from

[`BaseIndex`](../baseindex.md).[`evaluateIndexExpression`](../BaseIndex.md#evaluateindexexpression)

***

### getStats()

```ts
getStats(): IndexStats
```

Defined in: [packages/db/src/indexes/base-index.ts:74](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L74)

#### Returns

[`IndexStats`](../../interfaces/indexstats.md)

#### Inherited from

[`BaseIndex`](../baseindex.md).[`getStats`](../BaseIndex.md#getstats)

***

### inArrayLookup()

```ts
inArrayLookup(values): Set<TKey>
```

Defined in: [packages/db/src/indexes/btree-index.ts:236](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L236)

Performs an IN array lookup

#### Parameters

##### values

`any`[]

#### Returns

`Set`\<`TKey`\>

***

### initialize()

```ts
protected initialize(_options?): void
```

Defined in: [packages/db/src/indexes/btree-index.ts:59](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L59)

#### Parameters

##### \_options?

[`BTreeIndexOptions`](../../interfaces/btreeindexoptions.md)

#### Returns

`void`

#### Overrides

[`BaseIndex`](../baseindex.md).[`initialize`](../BaseIndex.md#initialize)

***

### lookup()

```ts
lookup(operation, value): Set<TKey>
```

Defined in: [packages/db/src/indexes/btree-index.ts:153](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L153)

Performs a lookup operation

#### Parameters

##### operation

`"eq"` | `"gt"` | `"gte"` | `"lt"` | `"lte"` | `"in"` | `"like"` | `"ilike"`

##### value

`any`

#### Returns

`Set`\<`TKey`\>

#### Overrides

[`BaseIndex`](../baseindex.md).[`lookup`](../BaseIndex.md#lookup)

***

### matchesField()

```ts
matchesField(fieldPath): boolean
```

Defined in: [packages/db/src/indexes/base-index.ts:66](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L66)

#### Parameters

##### fieldPath

`string`[]

#### Returns

`boolean`

#### Inherited from

[`BaseIndex`](../baseindex.md).[`matchesField`](../BaseIndex.md#matchesfield)

***

### rangeQuery()

```ts
rangeQuery(options): Set<TKey>
```

Defined in: [packages/db/src/indexes/btree-index.ts:205](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L205)

Performs a range query with options
This is more efficient for compound queries like "WHERE a > 5 AND a < 10"

#### Parameters

##### options

[`RangeQueryOptions`](../../interfaces/rangequeryoptions.md) = `{}`

#### Returns

`Set`\<`TKey`\>

***

### remove()

```ts
remove(key, item): void
```

Defined in: [packages/db/src/indexes/btree-index.ts:92](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L92)

Removes a value from the index

#### Parameters

##### key

`TKey`

##### item

`any`

#### Returns

`void`

#### Overrides

[`BaseIndex`](../baseindex.md).[`remove`](../BaseIndex.md#remove)

***

### supports()

```ts
supports(operation): boolean
```

Defined in: [packages/db/src/indexes/base-index.ts:62](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L62)

#### Parameters

##### operation

`"eq"` | `"gt"` | `"gte"` | `"lt"` | `"lte"` | `"in"` | `"like"` | `"ilike"`

#### Returns

`boolean`

#### Inherited from

[`BaseIndex`](../baseindex.md).[`supports`](../BaseIndex.md#supports)

***

### trackLookup()

```ts
protected trackLookup(startTime): void
```

Defined in: [packages/db/src/indexes/base-index.ts:92](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L92)

#### Parameters

##### startTime

`number`

#### Returns

`void`

#### Inherited from

[`BaseIndex`](../baseindex.md).[`trackLookup`](../BaseIndex.md#tracklookup)

***

### update()

```ts
update(
   key, 
   oldItem, 
   newItem): void
```

Defined in: [packages/db/src/indexes/btree-index.ts:124](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/btree-index.ts#L124)

Updates a value in the index

#### Parameters

##### key

`TKey`

##### oldItem

`any`

##### newItem

`any`

#### Returns

`void`

#### Overrides

[`BaseIndex`](../baseindex.md).[`update`](../BaseIndex.md#update)

***

### updateTimestamp()

```ts
protected updateTimestamp(): void
```

Defined in: [packages/db/src/indexes/base-index.ts:98](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/base-index.ts#L98)

#### Returns

`void`

#### Inherited from

[`BaseIndex`](../baseindex.md).[`updateTimestamp`](../BaseIndex.md#updatetimestamp)
