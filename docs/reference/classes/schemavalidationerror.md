---
id: SchemaValidationError
title: SchemaValidationError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: SchemaValidationError

Defined in: [packages/db/src/errors.ts:18](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L18)

## Extends

- [`TanStackDBError`](../tanstackdberror.md)

## Constructors

### new SchemaValidationError()

```ts
new SchemaValidationError(
   type, 
   issues, 
   message?): SchemaValidationError
```

Defined in: [packages/db/src/errors.ts:25](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L25)

#### Parameters

##### type

`"insert"` | `"update"`

##### issues

readonly `object`[]

##### message?

`string`

#### Returns

[`SchemaValidationError`](../schemavalidationerror.md)

#### Overrides

[`TanStackDBError`](../tanstackdberror.md).[`constructor`](../TanStackDBError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`cause`](../TanStackDBError.md#cause)

***

### issues

```ts
issues: readonly object[];
```

Defined in: [packages/db/src/errors.ts:20](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L20)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`message`](../TanStackDBError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`name`](../TanStackDBError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`stack`](../TanStackDBError.md#stack)

***

### type

```ts
type: "insert" | "update";
```

Defined in: [packages/db/src/errors.ts:19](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L19)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`prepareStackTrace`](../TanStackDBError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`stackTraceLimit`](../TanStackDBError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`captureStackTrace`](../TanStackDBError.md#capturestacktrace)
