---
id: BaseQueryBuilder
title: BaseQueryBuilder
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: BaseQueryBuilder\<TContext\>

Defined in: [packages/db/src/query/builder/index.ts:37](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L37)

## Type Parameters

• **TContext** *extends* [`Context`](../../interfaces/context.md) = [`Context`](../../interfaces/context.md)

## Constructors

### new BaseQueryBuilder()

```ts
new BaseQueryBuilder<TContext>(query): BaseQueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:40](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L40)

#### Parameters

##### query

`Partial`\<`QueryIR`\> = `{}`

#### Returns

[`BaseQueryBuilder`](../basequerybuilder.md)\<`TContext`\>

## Accessors

### fn

#### Get Signature

```ts
get fn(): object
```

Defined in: [packages/db/src/query/builder/index.ts:645](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L645)

Functional variants of the query builder
These are imperative function that are called for ery row.
Warning: that these cannot be optimized by the query compiler, and may prevent
some type of optimizations being possible.

##### Example

```ts
q.fn.select((row) => ({
  name: row.user.name.toUpperCase(),
  age: row.user.age + 1,
}))
```

##### Returns

`object`

###### having()

Filter grouped rows using a function that operates on each aggregated row
Warning: This cannot be optimized by the query compiler

###### Parameters

###### callback

(`row`) => `any`

A function that receives an aggregated row and returns a boolean

###### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with functional having filter applied

###### Example

```ts
// Functional having (not optimized)
query
  .from({ posts: postsCollection })
  .groupBy(({posts}) => posts.userId)
  .fn.having(row => row.count > 5)
```

###### select()

Select fields using a function that operates on each row
Warning: This cannot be optimized by the query compiler

###### Type Parameters

• **TFuncSelectResult**

###### Parameters

###### callback

(`row`) => `TFuncSelectResult`

A function that receives a row and returns the selected value

###### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<\{ \[K in string \| number \| symbol\]: (Omit\<TContext, "result"\> & \{ result: \{ \[K in string \| number \| symbol\]: TFuncSelectResult\[K\] \} \})\[K\] \}\>

A QueryBuilder with functional selection applied

###### Example

```ts
// Functional select (not optimized)
query
  .from({ users: usersCollection })
  .fn.select(row => ({
    name: row.users.name.toUpperCase(),
    age: row.users.age + 1,
  }))
```

###### where()

Filter rows using a function that operates on each row
Warning: This cannot be optimized by the query compiler

###### Parameters

###### callback

(`row`) => `any`

A function that receives a row and returns a boolean

###### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with functional filtering applied

###### Example

```ts
// Functional where (not optimized)
query
  .from({ users: usersCollection })
  .fn.where(row => row.users.name.startsWith('A'))
```

## Methods

### \_getQuery()

```ts
_getQuery(): QueryIR
```

Defined in: [packages/db/src/query/builder/index.ts:731](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L731)

#### Returns

`QueryIR`

***

### distinct()

```ts
distinct(): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:606](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L606)

Specify that the query should return distinct rows.
Deduplicates rows based on the selected columns.

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with distinct enabled

#### Example

```ts
// Get countries our users are from
query
  .from({ users: usersCollection })
  .select(({users}) => users.country)
  .distinct()
```

***

### from()

```ts
from<TSource>(source): QueryBuilder<{
  baseSchema: { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] };
  fromSourceName: keyof TSource & string;
  hasJoins: false;
  schema: { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] };
}>
```

Defined in: [packages/db/src/query/builder/index.ts:94](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L94)

Specify the source table or subquery for the query

#### Type Parameters

• **TSource** *extends* [`Source`](../../type-aliases/source.md)

#### Parameters

##### source

`TSource`

An object with a single key-value pair where the key is the table alias and the value is a Collection or subquery

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<\{
  `baseSchema`: \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \};
  `fromSourceName`: keyof `TSource` & `string`;
  `hasJoins`: `false`;
  `schema`: \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \};
 \}\>

A QueryBuilder with the specified source

#### Example

```ts
// Query from a collection
query.from({ users: usersCollection })

// Query from a subquery
const activeUsers = query.from({ u: usersCollection }).where(({u}) => u.active)
query.from({ activeUsers })
```

***

### fullJoin()

```ts
fullJoin<TSource>(source, onCallback): QueryBuilder<MergeContextWithJoinType<TContext, { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] }, "full">>
```

Defined in: [packages/db/src/query/builder/index.ts:285](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L285)

Perform a FULL JOIN with another table or subquery

#### Type Parameters

• **TSource** *extends* [`Source`](../../type-aliases/source.md)

#### Parameters

##### source

`TSource`

An object with a single key-value pair where the key is the table alias and the value is a Collection or subquery

##### onCallback

`JoinOnCallback`\<`MergeContext`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}\>\>

A function that receives table references and returns the join condition

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`MergeContextWithJoinType`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}, `"full"`\>\>

A QueryBuilder with the full joined table available

#### Example

```ts
// Full join users with posts
query
  .from({ users: usersCollection })
  .fullJoin({ posts: postsCollection }, ({users, posts}) => eq(users.id, posts.userId))
```

***

### groupBy()

```ts
groupBy(callback): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:529](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L529)

Group rows by one or more columns for aggregation

#### Parameters

##### callback

`GroupByCallback`\<`TContext`\>

A function that receives table references and returns the field(s) to group by

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with grouping applied (enables aggregate functions in SELECT and HAVING)

#### Example

```ts
// Group by a single column
query
  .from({ posts: postsCollection })
  .groupBy(({posts}) => posts.userId)
  .select(({posts, count}) => ({
    userId: posts.userId,
    postCount: count()
  }))

// Group by multiple columns
query
  .from({ sales: salesCollection })
  .groupBy(({sales}) => [sales.region, sales.category])
  .select(({sales, sum}) => ({
    region: sales.region,
    category: sales.category,
    totalSales: sum(sales.amount)
  }))
```

***

### having()

```ts
having(callback): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:365](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L365)

Filter grouped rows based on aggregate conditions

#### Parameters

##### callback

`WhereCallback`\<`TContext`\>

A function that receives table references and returns an expression

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with the having condition applied

#### Example

```ts
// Filter groups by count
query
  .from({ posts: postsCollection })
  .groupBy(({posts}) => posts.userId)
  .having(({posts}) => gt(count(posts.id), 5))

// Filter by average
query
  .from({ orders: ordersCollection })
  .groupBy(({orders}) => orders.customerId)
  .having(({orders}) => gt(avg(orders.total), 100))

// Multiple having calls are ANDed together
query
  .from({ orders: ordersCollection })
  .groupBy(({orders}) => orders.customerId)
  .having(({orders}) => gt(count(orders.id), 5))
  .having(({orders}) => gt(avg(orders.total), 100))
```

***

### innerJoin()

```ts
innerJoin<TSource>(source, onCallback): QueryBuilder<MergeContextWithJoinType<TContext, { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] }, "inner">>
```

Defined in: [packages/db/src/query/builder/index.ts:259](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L259)

Perform an INNER JOIN with another table or subquery

#### Type Parameters

• **TSource** *extends* [`Source`](../../type-aliases/source.md)

#### Parameters

##### source

`TSource`

An object with a single key-value pair where the key is the table alias and the value is a Collection or subquery

##### onCallback

`JoinOnCallback`\<`MergeContext`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}\>\>

A function that receives table references and returns the join condition

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`MergeContextWithJoinType`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}, `"inner"`\>\>

A QueryBuilder with the inner joined table available

#### Example

```ts
// Inner join users with posts
query
  .from({ users: usersCollection })
  .innerJoin({ posts: postsCollection }, ({users, posts}) => eq(users.id, posts.userId))
```

***

### join()

```ts
join<TSource, TJoinType>(
   source, 
   onCallback, 
type): QueryBuilder<MergeContextWithJoinType<TContext, { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] }, TJoinType>>
```

Defined in: [packages/db/src/query/builder/index.ts:137](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L137)

Join another table or subquery to the current query

#### Type Parameters

• **TSource** *extends* [`Source`](../../type-aliases/source.md)

• **TJoinType** *extends* `"full"` \| `"left"` \| `"right"` \| `"inner"` = `"left"`

#### Parameters

##### source

`TSource`

An object with a single key-value pair where the key is the table alias and the value is a Collection or subquery

##### onCallback

`JoinOnCallback`\<`MergeContext`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}\>\>

A function that receives table references and returns the join condition

##### type

`TJoinType` = `...`

The type of join: 'inner', 'left', 'right', or 'full' (defaults to 'left')

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`MergeContextWithJoinType`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}, `TJoinType`\>\>

A QueryBuilder with the joined table available

#### Example

```ts
// Left join users with posts
query
  .from({ users: usersCollection })
  .join({ posts: postsCollection }, ({users, posts}) => eq(users.id, posts.userId))

// Inner join with explicit type
query
  .from({ u: usersCollection })
  .join({ p: postsCollection }, ({u, p}) => eq(u.id, p.userId), 'inner')
```

// Join with a subquery
const activeUsers = query.from({ u: usersCollection }).where(({u}) => u.active)
query
  .from({ activeUsers })
  .join({ p: postsCollection }, ({u, p}) => eq(u.id, p.userId))

***

### leftJoin()

```ts
leftJoin<TSource>(source, onCallback): QueryBuilder<MergeContextWithJoinType<TContext, { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] }, "left">>
```

Defined in: [packages/db/src/query/builder/index.ts:207](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L207)

Perform a LEFT JOIN with another table or subquery

#### Type Parameters

• **TSource** *extends* [`Source`](../../type-aliases/source.md)

#### Parameters

##### source

`TSource`

An object with a single key-value pair where the key is the table alias and the value is a Collection or subquery

##### onCallback

`JoinOnCallback`\<`MergeContext`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}\>\>

A function that receives table references and returns the join condition

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`MergeContextWithJoinType`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}, `"left"`\>\>

A QueryBuilder with the left joined table available

#### Example

```ts
// Left join users with posts
query
  .from({ users: usersCollection })
  .leftJoin({ posts: postsCollection }, ({users, posts}) => eq(users.id, posts.userId))
```

***

### limit()

```ts
limit(count): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:561](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L561)

Limit the number of rows returned by the query
`orderBy` is required for `limit`

#### Parameters

##### count

`number`

Maximum number of rows to return

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with the limit applied

#### Example

```ts
// Get top 5 posts by likes
query
  .from({ posts: postsCollection })
  .orderBy(({posts}) => posts.likes, 'desc')
  .limit(5)
```

***

### offset()

```ts
offset(count): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:585](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L585)

Skip a number of rows before returning results
`orderBy` is required for `offset`

#### Parameters

##### count

`number`

Number of rows to skip

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with the offset applied

#### Example

```ts
// Get second page of results
query
  .from({ posts: postsCollection })
  .orderBy(({posts}) => posts.createdAt, 'desc')
  .offset(page * pageSize)
  .limit(pageSize)
```

***

### orderBy()

```ts
orderBy(callback, direction): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:479](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L479)

Sort the query results by one or more columns

#### Parameters

##### callback

`OrderByCallback`\<`TContext`\>

A function that receives table references and returns the field to sort by

##### direction

`OrderByDirection` = `...`

Sort direction: 'asc' for ascending, 'desc' for descending (defaults to 'asc')

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with the ordering applied

#### Example

```ts
// Sort by a single column
query
  .from({ users: usersCollection })
  .orderBy(({users}) => users.name)

// Sort descending
query
  .from({ users: usersCollection })
  .orderBy(({users}) => users.createdAt, 'desc')

// Multiple sorts (chain orderBy calls)
query
  .from({ users: usersCollection })
  .orderBy(({users}) => users.lastName)
  .orderBy(({users}) => users.firstName)
```

***

### rightJoin()

```ts
rightJoin<TSource>(source, onCallback): QueryBuilder<MergeContextWithJoinType<TContext, { [K in string | number | symbol]: { [K in string | number | symbol]: TSource[K] extends CollectionImpl<U, string | number, {}, StandardSchemaV1<unknown, unknown>, U> ? U : TSource[K] extends QueryBuilder<TContext> ? { [K in string | number | symbol]: ((...)[(...)] extends object ? any[any] : (...) extends (...) ? (...) : (...))[K] } : never }[K] }, "right">>
```

Defined in: [packages/db/src/query/builder/index.ts:233](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L233)

Perform a RIGHT JOIN with another table or subquery

#### Type Parameters

• **TSource** *extends* [`Source`](../../type-aliases/source.md)

#### Parameters

##### source

`TSource`

An object with a single key-value pair where the key is the table alias and the value is a Collection or subquery

##### onCallback

`JoinOnCallback`\<`MergeContext`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}\>\>

A function that receives table references and returns the join condition

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`MergeContextWithJoinType`\<`TContext`, \{ \[K in string \| number \| symbol\]: \{ \[K in string \| number \| symbol\]: TSource\[K\] extends CollectionImpl\<U, string \| number, \{\}, StandardSchemaV1\<unknown, unknown\>, U\> ? U : TSource\[K\] extends QueryBuilder\<TContext\> ? \{ \[K in string \| number \| symbol\]: ((...)\[(...)\] extends object ? any\[any\] : (...) extends (...) ? (...) : (...))\[K\] \} : never \}\[K\] \}, `"right"`\>\>

A QueryBuilder with the right joined table available

#### Example

```ts
// Right join users with posts
query
  .from({ users: usersCollection })
  .rightJoin({ posts: postsCollection }, ({users, posts}) => eq(users.id, posts.userId))
```

***

### select()

```ts
select<TSelectObject>(callback): QueryBuilder<{ [K in string | number | symbol]: (Omit<TContext, "result"> & { result: { [K in string | number | symbol]: ResultTypeFromSelect<TSelectObject>[K] } })[K] }>
```

Defined in: [packages/db/src/query/builder/index.ts:412](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L412)

Select specific columns or computed values from the query

#### Type Parameters

• **TSelectObject** *extends* `Record`\<`string`, 
  \| `BasicExpression`
  \| `Aggregate`\<`any`\>
  \| `RefProxy`\<`any`\>
  \| `RefProxyFor`\<`any`\>\>

#### Parameters

##### callback

(`refs`) => `TSelectObject`

A function that receives table references and returns an object with selected fields or expressions

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<\{ \[K in string \| number \| symbol\]: (Omit\<TContext, "result"\> & \{ result: \{ \[K in string \| number \| symbol\]: ResultTypeFromSelect\<TSelectObject\>\[K\] \} \})\[K\] \}\>

A QueryBuilder that returns only the selected fields

#### Example

```ts
// Select specific columns
query
  .from({ users: usersCollection })
  .select(({users}) => ({
    name: users.name,
    email: users.email
  }))

// Select with computed values
query
  .from({ users: usersCollection })
  .select(({users}) => ({
    fullName: concat(users.firstName, ' ', users.lastName),
    ageInMonths: mul(users.age, 12)
  }))

// Select with aggregates (requires GROUP BY)
query
  .from({ posts: postsCollection })
  .groupBy(({posts}) => posts.userId)
  .select(({posts, count}) => ({
    userId: posts.userId,
    postCount: count(posts.id)
  }))
```

***

### where()

```ts
where(callback): QueryBuilder<TContext>
```

Defined in: [packages/db/src/query/builder/index.ts:324](https://github.com/TanStack/db/blob/main/packages/db/src/query/builder/index.ts#L324)

Filter rows based on a condition

#### Parameters

##### callback

`WhereCallback`\<`TContext`\>

A function that receives table references and returns an expression

#### Returns

[`QueryBuilder`](../../type-aliases/querybuilder.md)\<`TContext`\>

A QueryBuilder with the where condition applied

#### Example

```ts
// Simple condition
query
  .from({ users: usersCollection })
  .where(({users}) => gt(users.age, 18))

// Multiple conditions
query
  .from({ users: usersCollection })
  .where(({users}) => and(
    gt(users.age, 18),
    eq(users.active, true)
  ))

// Multiple where calls are ANDed together
query
  .from({ users: usersCollection })
  .where(({users}) => gt(users.age, 18))
  .where(({users}) => eq(users.active, true))
```
