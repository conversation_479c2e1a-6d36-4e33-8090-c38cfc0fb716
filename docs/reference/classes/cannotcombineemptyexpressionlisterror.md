---
id: CannotCombineEmptyExpressionListError
title: CannotCombineEmptyExpressionListError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: CannotCombineEmptyExpressionListError

Defined in: [packages/db/src/errors.ts:546](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L546)

## Extends

- [`QueryOptimizerError`](../queryoptimizererror.md)

## Constructors

### new CannotCombineEmptyExpressionListError()

```ts
new CannotCombineEmptyExpressionListError(): CannotCombineEmptyExpressionListError
```

Defined in: [packages/db/src/errors.ts:547](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L547)

#### Returns

[`CannotCombineEmptyExpressionListError`](../cannotcombineemptyexpressionlisterror.md)

#### Overrides

[`QueryOptimizerError`](../queryoptimizererror.md).[`constructor`](../QueryOptimizerError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`cause`](../QueryOptimizerError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`message`](../QueryOptimizerError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`name`](../QueryOptimizerError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`stack`](../QueryOptimizerError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`prepareStackTrace`](../QueryOptimizerError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`stackTraceLimit`](../QueryOptimizerError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`QueryOptimizerError`](../queryoptimizererror.md).[`captureStackTrace`](../QueryOptimizerError.md#capturestacktrace)
