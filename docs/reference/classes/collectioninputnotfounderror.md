---
id: CollectionInputNotFoundError
title: CollectionInputNotFoundError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: CollectionInputNotFoundError

Defined in: [packages/db/src/errors.ts:350](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L350)

## Extends

- [`QueryCompilationError`](../querycompilationerror.md)

## Constructors

### new CollectionInputNotFoundError()

```ts
new CollectionInputNotFoundError(collectionId): CollectionInputNotFoundError
```

Defined in: [packages/db/src/errors.ts:351](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L351)

#### Parameters

##### collectionId

`string`

#### Returns

[`CollectionInputNotFoundError`](../collectioninputnotfounderror.md)

#### Overrides

[`QueryCompilationError`](../querycompilationerror.md).[`constructor`](../QueryCompilationError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`cause`](../QueryCompilationError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`message`](../QueryCompilationError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`name`](../QueryCompilationError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`stack`](../QueryCompilationError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`prepareStackTrace`](../QueryCompilationError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`stackTraceLimit`](../QueryCompilationError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`QueryCompilationError`](../querycompilationerror.md).[`captureStackTrace`](../QueryCompilationError.md#capturestacktrace)
