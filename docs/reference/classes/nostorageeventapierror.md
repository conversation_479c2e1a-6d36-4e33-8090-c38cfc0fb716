---
id: NoStorageEventApiError
title: NoStorageEventApiError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: NoStorageEventApiError

Defined in: [packages/db/src/errors.ts:503](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L503)

## Extends

- [`LocalStorageCollectionError`](../localstoragecollectionerror.md)

## Constructors

### new NoStorageEventApiError()

```ts
new NoStorageEventApiError(): NoStorageEventApiError
```

Defined in: [packages/db/src/errors.ts:504](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L504)

#### Returns

[`NoStorageEventApiError`](../nostorageeventapierror.md)

#### Overrides

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`constructor`](../LocalStorageCollectionError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`cause`](../LocalStorageCollectionError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`message`](../LocalStorageCollectionError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`name`](../LocalStorageCollectionError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`stack`](../LocalStorageCollectionError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`prepareStackTrace`](../LocalStorageCollectionError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`stackTraceLimit`](../LocalStorageCollectionError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`LocalStorageCollectionError`](../localstoragecollectionerror.md).[`captureStackTrace`](../LocalStorageCollectionError.md#capturestacktrace)
