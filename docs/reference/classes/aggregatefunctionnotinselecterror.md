---
id: AggregateFunctionNotInSelectError
title: AggregateFunctionNotInSelectError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: AggregateFunctionNotInSelectError

Defined in: [packages/db/src/errors.ts:451](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L451)

## Extends

- [`GroupByError`](../groupbyerror.md)

## Constructors

### new AggregateFunctionNotInSelectError()

```ts
new AggregateFunctionNotInSelectError(functionName): AggregateFunctionNotInSelectError
```

Defined in: [packages/db/src/errors.ts:452](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L452)

#### Parameters

##### functionName

`string`

#### Returns

[`AggregateFunctionNotInSelectError`](../aggregatefunctionnotinselecterror.md)

#### Overrides

[`GroupByError`](../groupbyerror.md).[`constructor`](../GroupByError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`cause`](../GroupByError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`message`](../GroupByError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`name`](../GroupByError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`stack`](../GroupByError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`prepareStackTrace`](../GroupByError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`stackTraceLimit`](../GroupByError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`GroupByError`](../groupbyerror.md).[`captureStackTrace`](../GroupByError.md#capturestacktrace)
