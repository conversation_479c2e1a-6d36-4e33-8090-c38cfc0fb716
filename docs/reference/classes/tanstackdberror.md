---
id: TanStackDBError
title: TanStackDBError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: TanStackDBError

Defined in: [packages/db/src/errors.ts:2](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L2)

## Extends

- `Error`

## Extended by

- [`NonRetriableError`](../nonretriableerror.md)
- [`SchemaValidationError`](../schemavalidationerror.md)
- [`CollectionConfigurationError`](../collectionconfigurationerror.md)
- [`CollectionStateError`](../collectionstateerror.md)
- [`CollectionOperationError`](../collectionoperationerror.md)
- [`MissingHandlerError`](../missinghandlererror.md)
- [`TransactionError`](../transactionerror.md)
- [`QueryBuilderError`](../querybuildererror.md)
- [`QueryCompilationError`](../querycompilationerror.md)
- [`JoinError`](../joinerror.md)
- [`GroupByError`](../groupbyerror.md)
- [`StorageError`](../storageerror.md)
- [`SyncCleanupError`](../synccleanuperror.md)
- [`QueryOptimizerError`](../queryoptimizererror.md)

## Constructors

### new TanStackDBError()

```ts
new TanStackDBError(message): TanStackDBError
```

Defined in: [packages/db/src/errors.ts:3](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L3)

#### Parameters

##### message

`string`

#### Returns

[`TanStackDBError`](../tanstackdberror.md)

#### Overrides

```ts
Error.constructor
```

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

```ts
Error.cause
```

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

```ts
Error.message
```

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

```ts
Error.name
```

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

```ts
Error.stack
```

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

```ts
Error.prepareStackTrace
```

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

```ts
Error.stackTraceLimit
```

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

```ts
Error.captureStackTrace
```
