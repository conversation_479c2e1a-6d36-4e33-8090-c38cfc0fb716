---
id: LazyIndexWrapper
title: LazyIndexWrapper
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: LazyIndexWrapper\<TKey\>

Defined in: [packages/db/src/indexes/lazy-index.ts:39](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L39)

Wrapper that defers index creation until first sync

## Type Parameters

• **TKey** *extends* `string` \| `number` = `string` \| `number`

## Constructors

### new LazyIndexWrapper()

```ts
new LazyIndexWrapper<TKey>(
   id, 
   expression, 
   name, 
   resolver, 
   options, 
collectionEntries?): LazyIndexWrapper<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:43](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L43)

#### Parameters

##### id

`number`

##### expression

`BasicExpression`

##### name

`undefined` | `string`

##### resolver

[`IndexResolver`](../../type-aliases/indexresolver.md)\<`TKey`\>

##### options

`any`

##### collectionEntries?

`Iterable`\<\[`TKey`, `any`\], `any`, `any`\>

#### Returns

[`LazyIndexWrapper`](../lazyindexwrapper.md)\<`TKey`\>

## Methods

### getExpression()

```ts
getExpression(): BasicExpression
```

Defined in: [packages/db/src/indexes/lazy-index.ts:118](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L118)

Get the index expression

#### Returns

`BasicExpression`

***

### getId()

```ts
getId(): number
```

Defined in: [packages/db/src/indexes/lazy-index.ts:104](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L104)

Get the index ID

#### Returns

`number`

***

### getName()

```ts
getName(): undefined | string
```

Defined in: [packages/db/src/indexes/lazy-index.ts:111](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L111)

Get the index name

#### Returns

`undefined` \| `string`

***

### getResolved()

```ts
getResolved(): BaseIndex<TKey>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:92](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L92)

Get resolved index (throws if not ready)

#### Returns

[`BaseIndex`](../baseindex.md)\<`TKey`\>

***

### isResolved()

```ts
isResolved(): boolean
```

Defined in: [packages/db/src/indexes/lazy-index.ts:85](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L85)

Check if already resolved

#### Returns

`boolean`

***

### resolve()

```ts
resolve(): Promise<BaseIndex<TKey>>
```

Defined in: [packages/db/src/indexes/lazy-index.ts:69](https://github.com/TanStack/db/blob/main/packages/db/src/indexes/lazy-index.ts#L69)

Resolve the actual index

#### Returns

`Promise`\<[`BaseIndex`](../baseindex.md)\<`TKey`\>\>
