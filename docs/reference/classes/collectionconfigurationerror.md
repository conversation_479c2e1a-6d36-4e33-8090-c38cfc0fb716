---
id: CollectionConfigurationError
title: CollectionConfigurationError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: CollectionConfigurationError

Defined in: [packages/db/src/errors.ts:45](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L45)

## Extends

- [`TanStackDBError`](../tanstackdberror.md)

## Extended by

- [`CollectionRequiresConfigError`](../collectionrequiresconfigerror.md)
- [`CollectionRequiresSyncConfigError`](../collectionrequiressyncconfigerror.md)
- [`InvalidSchemaError`](../invalidschemaerror.md)
- [`SchemaMustBeSynchronousError`](../schemamustbesynchronouserror.md)

## Constructors

### new CollectionConfigurationError()

```ts
new CollectionConfigurationError(message): CollectionConfigurationError
```

Defined in: [packages/db/src/errors.ts:46](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L46)

#### Parameters

##### message

`string`

#### Returns

[`CollectionConfigurationError`](../collectionconfigurationerror.md)

#### Overrides

[`TanStackDBError`](../tanstackdberror.md).[`constructor`](../TanStackDBError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`cause`](../TanStackDBError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`message`](../TanStackDBError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`name`](../TanStackDBError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`stack`](../TanStackDBError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`prepareStackTrace`](../TanStackDBError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`stackTraceLimit`](../TanStackDBError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`TanStackDBError`](../tanstackdberror.md).[`captureStackTrace`](../TanStackDBError.md#capturestacktrace)
