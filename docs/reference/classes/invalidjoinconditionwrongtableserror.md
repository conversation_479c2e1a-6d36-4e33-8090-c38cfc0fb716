---
id: InvalidJoinConditionWrongTablesError
title: InvalidJoinConditionWrongTablesError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: InvalidJoinConditionWrongTablesError

Defined in: [packages/db/src/errors.ts:410](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L410)

## Extends

- [`JoinError`](../joinerror.md)

## Constructors

### new InvalidJoinConditionWrongTablesError()

```ts
new InvalidJoinConditionWrongTablesError(
   leftTableAlias, 
   rightTableAlias, 
   mainTableAlias, 
   joinedTableAlias): InvalidJoinConditionWrongTablesError
```

Defined in: [packages/db/src/errors.ts:411](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L411)

#### Parameters

##### leftTableAlias

`string`

##### rightTableAlias

`string`

##### mainTableAlias

`string`

##### joinedTableAlias

`string`

#### Returns

[`InvalidJoinConditionWrongTablesError`](../invalidjoinconditionwrongtableserror.md)

#### Overrides

[`JoinError`](../joinerror.md).[`constructor`](../JoinError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`JoinError`](../joinerror.md).[`cause`](../JoinError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`JoinError`](../joinerror.md).[`message`](../JoinError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`JoinError`](../joinerror.md).[`name`](../JoinError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`JoinError`](../joinerror.md).[`stack`](../JoinError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`JoinError`](../joinerror.md).[`prepareStackTrace`](../JoinError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`JoinError`](../joinerror.md).[`stackTraceLimit`](../JoinError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`JoinError`](../joinerror.md).[`captureStackTrace`](../JoinError.md#capturestacktrace)
