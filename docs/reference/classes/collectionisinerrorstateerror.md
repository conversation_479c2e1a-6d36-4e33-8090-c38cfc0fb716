---
id: CollectionIsInErrorStateError
title: CollectionIsInErrorStateError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: CollectionIsInErrorStateError

Defined in: [packages/db/src/errors.ts:100](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L100)

## Extends

- [`CollectionStateError`](../collectionstateerror.md)

## Constructors

### new CollectionIsInErrorStateError()

```ts
new CollectionIsInErrorStateError(): CollectionIsInErrorStateError
```

Defined in: [packages/db/src/errors.ts:101](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L101)

#### Returns

[`CollectionIsInErrorStateError`](../collectionisinerrorstateerror.md)

#### Overrides

[`CollectionStateError`](../collectionstateerror.md).[`constructor`](../CollectionStateError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`cause`](../CollectionStateError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`message`](../CollectionStateError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`name`](../CollectionStateError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`stack`](../CollectionStateError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`prepareStackTrace`](../CollectionStateError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`stackTraceLimit`](../CollectionStateError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`CollectionStateError`](../collectionstateerror.md).[`captureStackTrace`](../CollectionStateError.md#capturestacktrace)
