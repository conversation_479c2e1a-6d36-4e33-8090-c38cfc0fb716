---
id: SortedMap
title: SortedMap
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: SortedMap\<TKey, TValue\>

Defined in: [packages/db/src/SortedMap.ts:6](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L6)

A Map implementation that keeps its entries sorted based on a comparator function

## Type Parameters

• **TKey**

The type of keys in the map

• **TValue**

The type of values in the map

## Constructors

### new SortedMap()

```ts
new SortedMap<TKey, TValue>(comparator?): SortedMap<TKey, TValue>
```

Defined in: [packages/db/src/SortedMap.ts:16](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L16)

Creates a new SortedMap instance

#### Parameters

##### comparator?

(`a`, `b`) => `number`

Optional function to compare values for sorting

#### Returns

[`SortedMap`](../sortedmap.md)\<`TKey`, `TValue`\>

## Accessors

### size

#### Get Signature

```ts
get size(): number
```

Defined in: [packages/db/src/SortedMap.ts:138](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L138)

Gets the number of key-value pairs in the map

##### Returns

`number`

## Methods

### \[iterator\]()

```ts
iterator: IterableIterator<[TKey, TValue]>
```

Defined in: [packages/db/src/SortedMap.ts:147](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L147)

Default iterator that returns entries in sorted order

#### Returns

`IterableIterator`\<\[`TKey`, `TValue`\]\>

An iterator for the map's entries

***

### clear()

```ts
clear(): void
```

Defined in: [packages/db/src/SortedMap.ts:130](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L130)

Removes all key-value pairs from the map

#### Returns

`void`

***

### delete()

```ts
delete(key): boolean
```

Defined in: [packages/db/src/SortedMap.ts:106](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L106)

Removes a key-value pair from the map

#### Parameters

##### key

`TKey`

The key to remove

#### Returns

`boolean`

True if the key was found and removed, false otherwise

***

### entries()

```ts
entries(): IterableIterator<[TKey, TValue]>
```

Defined in: [packages/db/src/SortedMap.ts:158](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L158)

Returns an iterator for the map's entries in sorted order

#### Returns

`IterableIterator`\<\[`TKey`, `TValue`\]\>

An iterator for the map's entries

***

### forEach()

```ts
forEach(callbackfn): void
```

Defined in: [packages/db/src/SortedMap.ts:189](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L189)

Executes a callback function for each key-value pair in the map in sorted order

#### Parameters

##### callbackfn

(`value`, `key`, `map`) => `void`

Function to execute for each entry

#### Returns

`void`

***

### get()

```ts
get(key): undefined | TValue
```

Defined in: [packages/db/src/SortedMap.ts:96](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L96)

Gets a value by its key

#### Parameters

##### key

`TKey`

The key to look up

#### Returns

`undefined` \| `TValue`

The value associated with the key, or undefined if not found

***

### has()

```ts
has(key): boolean
```

Defined in: [packages/db/src/SortedMap.ts:123](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L123)

Checks if a key exists in the map

#### Parameters

##### key

`TKey`

The key to check

#### Returns

`boolean`

True if the key exists, false otherwise

***

### keys()

```ts
keys(): IterableIterator<TKey>
```

Defined in: [packages/db/src/SortedMap.ts:167](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L167)

Returns an iterator for the map's keys in sorted order

#### Returns

`IterableIterator`\<`TKey`\>

An iterator for the map's keys

***

### set()

```ts
set(key, value): this
```

Defined in: [packages/db/src/SortedMap.ts:73](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L73)

Sets a key-value pair in the map and maintains sort order

#### Parameters

##### key

`TKey`

The key to set

##### value

`TValue`

The value to associate with the key

#### Returns

`this`

This SortedMap instance for chaining

***

### values()

```ts
values(): IterableIterator<TValue>
```

Defined in: [packages/db/src/SortedMap.ts:176](https://github.com/TanStack/db/blob/main/packages/db/src/SortedMap.ts#L176)

Returns an iterator for the map's values in sorted order

#### Returns

`IterableIterator`\<`TValue`\>

An iterator for the map's values
