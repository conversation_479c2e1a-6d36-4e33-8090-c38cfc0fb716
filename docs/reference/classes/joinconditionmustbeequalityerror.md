---
id: JoinConditionMustBeEqualityError
title: JoinConditionMustBeEqualityError
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: JoinConditionMustBeEqualityError

Defined in: [packages/db/src/errors.ts:310](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L310)

## Extends

- [`QueryBuilderError`](../querybuildererror.md)

## Constructors

### new JoinConditionMustBeEqualityError()

```ts
new JoinConditionMustBeEqualityError(): JoinConditionMustBeEqualityError
```

Defined in: [packages/db/src/errors.ts:311](https://github.com/TanStack/db/blob/main/packages/db/src/errors.ts#L311)

#### Returns

[`JoinConditionMustBeEqualityError`](../joinconditionmustbeequalityerror.md)

#### Overrides

[`QueryBuilderError`](../querybuildererror.md).[`constructor`](../QueryBuilderError.md#constructors)

## Properties

### cause?

```ts
optional cause: unknown;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es2022.error.d.ts:26

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`cause`](../QueryBuilderError.md#cause)

***

### message

```ts
message: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1077

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`message`](../QueryBuilderError.md#message-1)

***

### name

```ts
name: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1076

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`name`](../QueryBuilderError.md#name)

***

### stack?

```ts
optional stack: string;
```

Defined in: node\_modules/.pnpm/typescript@5.8.2/node\_modules/typescript/lib/lib.es5.d.ts:1078

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`stack`](../QueryBuilderError.md#stack)

***

### prepareStackTrace()?

```ts
static optional prepareStackTrace: (err, stackTraces) => any;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:143

Optional override for formatting stack traces

#### Parameters

##### err

`Error`

##### stackTraces

`CallSite`[]

#### Returns

`any`

#### See

https://v8.dev/docs/stack-trace-api#customizing-stack-traces

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`prepareStackTrace`](../QueryBuilderError.md#preparestacktrace)

***

### stackTraceLimit

```ts
static stackTraceLimit: number;
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:145

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`stackTraceLimit`](../QueryBuilderError.md#stacktracelimit)

## Methods

### captureStackTrace()

```ts
static captureStackTrace(targetObject, constructorOpt?): void
```

Defined in: node\_modules/.pnpm/@types+node@22.13.10/node\_modules/@types/node/globals.d.ts:136

Create .stack property on a target object

#### Parameters

##### targetObject

`object`

##### constructorOpt?

`Function`

#### Returns

`void`

#### Inherited from

[`QueryBuilderError`](../querybuildererror.md).[`captureStackTrace`](../QueryBuilderError.md#capturestacktrace)
