---
id: Transaction
title: Transaction
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# Class: Transaction\<T\>

Defined in: [packages/db/src/transactions.ts:116](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L116)

## Type Parameters

• **T** *extends* `object` = `Record`\<`string`, `unknown`\>

## Constructors

### new Transaction()

```ts
new Transaction<T>(config): Transaction<T>
```

Defined in: [packages/db/src/transactions.ts:131](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L131)

#### Parameters

##### config

[`TransactionConfig`](../../interfaces/transactionconfig.md)\<`T`\>

#### Returns

[`Transaction`](../transaction.md)\<`T`\>

## Properties

### autoCommit

```ts
autoCommit: boolean;
```

Defined in: [packages/db/src/transactions.ts:122](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L122)

***

### createdAt

```ts
createdAt: Date;
```

Defined in: [packages/db/src/transactions.ts:123](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L123)

***

### error?

```ts
optional error: object;
```

Defined in: [packages/db/src/transactions.ts:126](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L126)

#### error

```ts
error: Error;
```

#### message

```ts
message: string;
```

***

### id

```ts
id: string;
```

Defined in: [packages/db/src/transactions.ts:117](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L117)

***

### isPersisted

```ts
isPersisted: Deferred<Transaction<T>>;
```

Defined in: [packages/db/src/transactions.ts:121](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L121)

***

### metadata

```ts
metadata: Record<string, unknown>;
```

Defined in: [packages/db/src/transactions.ts:125](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L125)

***

### mutationFn

```ts
mutationFn: MutationFn<T>;
```

Defined in: [packages/db/src/transactions.ts:119](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L119)

***

### mutations

```ts
mutations: PendingMutation<T, OperationType, Collection<T, any, any, any, any>>[];
```

Defined in: [packages/db/src/transactions.ts:120](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L120)

***

### sequenceNumber

```ts
sequenceNumber: number;
```

Defined in: [packages/db/src/transactions.ts:124](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L124)

***

### state

```ts
state: TransactionState;
```

Defined in: [packages/db/src/transactions.ts:118](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L118)

## Methods

### applyMutations()

```ts
applyMutations(mutations): void
```

Defined in: [packages/db/src/transactions.ts:212](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L212)

#### Parameters

##### mutations

[`PendingMutation`](../../interfaces/pendingmutation.md)\<`any`, [`OperationType`](../../type-aliases/operationtype.md), [`Collection`](../../interfaces/collection.md)\<`any`, `any`, `any`, `any`, `any`\>\>[]

#### Returns

`void`

***

### commit()

```ts
commit(): Promise<Transaction<T>>
```

Defined in: [packages/db/src/transactions.ts:349](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L349)

Commit the transaction and execute the mutation function

#### Returns

`Promise`\<[`Transaction`](../transaction.md)\<`T`\>\>

Promise that resolves to this transaction when complete

#### Examples

```ts
// Manual commit (when autoCommit is false)
const tx = createTransaction({
  autoCommit: false,
  mutationFn: async ({ transaction }) => {
    await api.saveChanges(transaction.mutations)
  }
})

tx.mutate(() => {
  collection.insert({ id: "1", text: "Buy milk" })
})

await tx.commit() // Manually commit
```

```ts
// Handle commit errors
try {
  const tx = createTransaction({
    mutationFn: async () => { throw new Error("API failed") }
  })

  tx.mutate(() => {
    collection.insert({ id: "1", text: "Item" })
  })

  await tx.commit()
} catch (error) {
  console.log('Commit failed, transaction rolled back:', error)
}
```

```ts
// Check transaction state after commit
await tx.commit()
console.log(tx.state) // "completed" or "failed"
```

***

### compareCreatedAt()

```ts
compareCreatedAt(other): number
```

Defined in: [packages/db/src/transactions.ts:395](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L395)

Compare two transactions by their createdAt time and sequence number in order
to sort them in the order they were created.

#### Parameters

##### other

[`Transaction`](../transaction.md)\<`any`\>

The other transaction to compare to

#### Returns

`number`

-1 if this transaction was created before the other, 1 if it was created after, 0 if they were created at the same time

***

### mutate()

```ts
mutate(callback): Transaction<T>
```

Defined in: [packages/db/src/transactions.ts:193](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L193)

Execute collection operations within this transaction

#### Parameters

##### callback

() => `void`

Function containing collection operations to group together

#### Returns

[`Transaction`](../transaction.md)\<`T`\>

This transaction for chaining

#### Examples

```ts
// Group multiple operations
const tx = createTransaction({ mutationFn: async () => {
  // Send to API
}})

tx.mutate(() => {
  collection.insert({ id: "1", text: "Buy milk" })
  collection.update("2", draft => { draft.completed = true })
  collection.delete("3")
})

await tx.isPersisted.promise
```

```ts
// Handle mutate errors
try {
  tx.mutate(() => {
    collection.insert({ id: "invalid" }) // This might throw
  })
} catch (error) {
  console.log('Mutation failed:', error)
}
```

```ts
// Manual commit control
const tx = createTransaction({ autoCommit: false, mutationFn: async () => {} })

tx.mutate(() => {
  collection.insert({ id: "1", text: "Item" })
})

// Commit later when ready
await tx.commit()
```

***

### rollback()

```ts
rollback(config?): Transaction<T>
```

Defined in: [packages/db/src/transactions.ts:266](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L266)

Rollback the transaction and any conflicting transactions

#### Parameters

##### config?

Configuration for rollback behavior

###### isSecondaryRollback?

`boolean`

#### Returns

[`Transaction`](../transaction.md)\<`T`\>

This transaction for chaining

#### Examples

```ts
// Manual rollback
const tx = createTransaction({ mutationFn: async () => {
  // Send to API
}})

tx.mutate(() => {
  collection.insert({ id: "1", text: "Buy milk" })
})

// Rollback if needed
if (shouldCancel) {
  tx.rollback()
}
```

```ts
// Handle rollback cascade (automatic)
const tx1 = createTransaction({ mutationFn: async () => {} })
const tx2 = createTransaction({ mutationFn: async () => {} })

tx1.mutate(() => collection.update("1", draft => { draft.value = "A" }))
tx2.mutate(() => collection.update("1", draft => { draft.value = "B" })) // Same item

tx1.rollback() // This will also rollback tx2 due to conflict
```

```ts
// Handle rollback in error scenarios
try {
  await tx.isPersisted.promise
} catch (error) {
  console.log('Transaction was rolled back:', error)
  // Transaction automatically rolled back on mutation function failure
}
```

***

### setState()

```ts
setState(newState): void
```

Defined in: [packages/db/src/transactions.ts:146](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L146)

#### Parameters

##### newState

[`TransactionState`](../../type-aliases/transactionstate.md)

#### Returns

`void`

***

### touchCollection()

```ts
touchCollection(): void
```

Defined in: [packages/db/src/transactions.ts:294](https://github.com/TanStack/db/blob/main/packages/db/src/transactions.ts#L294)

#### Returns

`void`
