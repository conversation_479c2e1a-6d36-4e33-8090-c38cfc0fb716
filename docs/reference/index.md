---
id: "@tanstack/db"
title: "@tanstack/db"
---

<!-- DO NOT EDIT: this page is autogenerated from the type comments -->

# @tanstack/db

## Classes

- [AggregateFunctionNotInSelectError](../classes/aggregatefunctionnotinselecterror.md)
- [BaseIndex](../classes/baseindex.md)
- [BaseQueryBuilder](../classes/basequerybuilder.md)
- [BTreeIndex](../classes/btreeindex.md)
- [CannotCombineEmptyExpressionListError](../classes/cannotcombineemptyexpressionlisterror.md)
- [CollectionConfigurationError](../classes/collectionconfigurationerror.md)
- [CollectionImpl](../classes/collectionimpl.md)
- [CollectionInErrorStateError](../classes/collectioninerrorstateerror.md)
- [CollectionInputNotFoundError](../classes/collectioninputnotfounderror.md)
- [CollectionIsInErrorStateError](../classes/collectionisinerrorstateerror.md)
- [CollectionOperationError](../classes/collectionoperationerror.md)
- [CollectionRequiresConfigError](../classes/collectionrequiresconfigerror.md)
- [CollectionRequiresSyncConfigError](../classes/collectionrequiressyncconfigerror.md)
- [CollectionStateError](../classes/collectionstateerror.md)
- [DeleteKeyNotFoundError](../classes/deletekeynotfounderror.md)
- [DistinctRequiresSelectError](../classes/distinctrequiresselecterror.md)
- [DuplicateKeyError](../classes/duplicatekeyerror.md)
- [DuplicateKeySyncError](../classes/duplicatekeysyncerror.md)
- [EmptyReferencePathError](../classes/emptyreferencepatherror.md)
- [GroupByError](../classes/groupbyerror.md)
- [HavingRequiresGroupByError](../classes/havingrequiresgroupbyerror.md)
- [IndexProxy](../classes/indexproxy.md)
- [InvalidCollectionStatusTransitionError](../classes/invalidcollectionstatustransitionerror.md)
- [InvalidJoinConditionSameTableError](../classes/invalidjoinconditionsametableerror.md)
- [InvalidJoinConditionTableMismatchError](../classes/invalidjoinconditiontablemismatcherror.md)
- [InvalidJoinConditionWrongTablesError](../classes/invalidjoinconditionwrongtableserror.md)
- [InvalidSchemaError](../classes/invalidschemaerror.md)
- [InvalidSourceError](../classes/invalidsourceerror.md)
- [InvalidStorageDataFormatError](../classes/invalidstoragedataformaterror.md)
- [InvalidStorageObjectFormatError](../classes/invalidstorageobjectformaterror.md)
- [JoinConditionMustBeEqualityError](../classes/joinconditionmustbeequalityerror.md)
- [JoinError](../classes/joinerror.md)
- [KeyUpdateNotAllowedError](../classes/keyupdatenotallowederror.md)
- [LazyIndexWrapper](../classes/lazyindexwrapper.md)
- [LimitOffsetRequireOrderByError](../classes/limitoffsetrequireorderbyerror.md)
- [LocalStorageCollectionError](../classes/localstoragecollectionerror.md)
- [MissingDeleteHandlerError](../classes/missingdeletehandlererror.md)
- [MissingHandlerError](../classes/missinghandlererror.md)
- [MissingInsertHandlerError](../classes/missinginserthandlererror.md)
- [MissingMutationFunctionError](../classes/missingmutationfunctionerror.md)
- [MissingUpdateArgumentError](../classes/missingupdateargumenterror.md)
- [MissingUpdateHandlerError](../classes/missingupdatehandlererror.md)
- [NegativeActiveSubscribersError](../classes/negativeactivesubscriberserror.md)
- [NoKeysPassedToDeleteError](../classes/nokeyspassedtodeleteerror.md)
- [NoKeysPassedToUpdateError](../classes/nokeyspassedtoupdateerror.md)
- [NonAggregateExpressionNotInGroupByError](../classes/nonaggregateexpressionnotingroupbyerror.md)
- [NonRetriableError](../classes/nonretriableerror.md)
- [NoPendingSyncTransactionCommitError](../classes/nopendingsynctransactioncommiterror.md)
- [NoPendingSyncTransactionWriteError](../classes/nopendingsynctransactionwriteerror.md)
- [NoStorageAvailableError](../classes/nostorageavailableerror.md)
- [NoStorageEventApiError](../classes/nostorageeventapierror.md)
- [OnlyOneSourceAllowedError](../classes/onlyonesourceallowederror.md)
- [QueryBuilderError](../classes/querybuildererror.md)
- [QueryCompilationError](../classes/querycompilationerror.md)
- [QueryMustHaveFromClauseError](../classes/querymusthavefromclauseerror.md)
- [QueryOptimizerError](../classes/queryoptimizererror.md)
- [SchemaMustBeSynchronousError](../classes/schemamustbesynchronouserror.md)
- [SchemaValidationError](../classes/schemavalidationerror.md)
- [SerializationError](../classes/serializationerror.md)
- [SortedMap](../classes/sortedmap.md)
- [StorageError](../classes/storageerror.md)
- [StorageKeyRequiredError](../classes/storagekeyrequirederror.md)
- [SubQueryMustHaveFromClauseError](../classes/subquerymusthavefromclauseerror.md)
- [SyncCleanupError](../classes/synccleanuperror.md)
- [SyncTransactionAlreadyCommittedError](../classes/synctransactionalreadycommittederror.md)
- [SyncTransactionAlreadyCommittedWriteError](../classes/synctransactionalreadycommittedwriteerror.md)
- [TanStackDBError](../classes/tanstackdberror.md)
- [Transaction](../classes/transaction.md)
- [TransactionAlreadyCompletedRollbackError](../classes/transactionalreadycompletedrollbackerror.md)
- [TransactionError](../classes/transactionerror.md)
- [TransactionNotPendingCommitError](../classes/transactionnotpendingcommiterror.md)
- [TransactionNotPendingMutateError](../classes/transactionnotpendingmutateerror.md)
- [UndefinedKeyError](../classes/undefinedkeyerror.md)
- [UnknownExpressionTypeError](../classes/unknownexpressiontypeerror.md)
- [UnknownFunctionError](../classes/unknownfunctionerror.md)
- [UnknownHavingExpressionTypeError](../classes/unknownhavingexpressiontypeerror.md)
- [UnsupportedAggregateFunctionError](../classes/unsupportedaggregatefunctionerror.md)
- [UnsupportedFromTypeError](../classes/unsupportedfromtypeerror.md)
- [UnsupportedJoinSourceTypeError](../classes/unsupportedjoinsourcetypeerror.md)
- [UnsupportedJoinTypeError](../classes/unsupportedjointypeerror.md)
- [UpdateKeyNotFoundError](../classes/updatekeynotfounderror.md)

## Interfaces

- [BTreeIndexOptions](../interfaces/btreeindexoptions.md)
- [ChangeMessage](../interfaces/changemessage.md)
- [Collection](../interfaces/collection.md)
- [CollectionConfig](../interfaces/collectionconfig.md)
- [Context](../interfaces/context.md)
- [CreateOptimisticActionsOptions](../interfaces/createoptimisticactionsoptions.md)
- [CurrentStateAsChangesOptions](../interfaces/currentstateaschangesoptions.md)
- [IndexOptions](../interfaces/indexoptions.md)
- [IndexStats](../interfaces/indexstats.md)
- [InsertConfig](../interfaces/insertconfig.md)
- [LiveQueryCollectionConfig](../interfaces/livequerycollectionconfig.md)
- [LocalOnlyCollectionConfig](../interfaces/localonlycollectionconfig.md)
- [LocalOnlyCollectionUtils](../interfaces/localonlycollectionutils.md)
- [LocalStorageCollectionConfig](../interfaces/localstoragecollectionconfig.md)
- [LocalStorageCollectionUtils](../interfaces/localstoragecollectionutils.md)
- [OperationConfig](../interfaces/operationconfig.md)
- [OptimisticChangeMessage](../interfaces/optimisticchangemessage.md)
- [PendingMutation](../interfaces/pendingmutation.md)
- [RangeQueryOptions](../interfaces/rangequeryoptions.md)
- [SubscribeChangesOptions](../interfaces/subscribechangesoptions.md)
- [SyncConfig](../interfaces/syncconfig.md)
- [TransactionConfig](../interfaces/transactionconfig.md)

## Type Aliases

- [ChangeListener](../type-aliases/changelistener.md)
- [ChangesPayload](../type-aliases/changespayload.md)
- [ClearStorageFn](../type-aliases/clearstoragefn.md)
- [CollectionStatus](../type-aliases/collectionstatus.md)
- [DeleteMutationFn](../type-aliases/deletemutationfn.md)
- [DeleteMutationFnParams](../type-aliases/deletemutationfnparams.md)
- [Fn](../type-aliases/fn.md)
- [GetResult](../type-aliases/getresult.md)
- [GetStorageSizeFn](../type-aliases/getstoragesizefn.md)
- [IndexConstructor](../type-aliases/indexconstructor.md)
- [IndexOperation](../type-aliases/indexoperation.md)
- [IndexResolver](../type-aliases/indexresolver.md)
- [InferSchemaInput](../type-aliases/inferschemainput.md)
- [InferSchemaOutput](../type-aliases/inferschemaoutput.md)
- [InitialQueryBuilder](../type-aliases/initialquerybuilder.md)
- [InputRow](../type-aliases/inputrow.md)
- [InsertMutationFn](../type-aliases/insertmutationfn.md)
- [InsertMutationFnParams](../type-aliases/insertmutationfnparams.md)
- [KeyedNamespacedRow](../type-aliases/keyednamespacedrow.md)
- [KeyedStream](../type-aliases/keyedstream.md)
- [MutationFn](../type-aliases/mutationfn.md)
- [MutationFnParams](../type-aliases/mutationfnparams.md)
- [NamespacedAndKeyedStream](../type-aliases/namespacedandkeyedstream.md)
- [NamespacedRow](../type-aliases/namespacedrow.md)
- [NonEmptyArray](../type-aliases/nonemptyarray.md)
- [OperationType](../type-aliases/operationtype.md)
- [QueryBuilder](../type-aliases/querybuilder.md)
- [Ref](../type-aliases/ref.md)
- [ResolveInsertInput](../type-aliases/resolveinsertinput.md)
- [ResolveTransactionChanges](../type-aliases/resolvetransactionchanges.md)
- [ResolveType](../type-aliases/resolvetype.md)
- [ResultStream](../type-aliases/resultstream.md)
- [Row](../type-aliases/row.md)
- [Source](../type-aliases/source.md)
- [StandardSchema](../type-aliases/standardschema.md)
- [StandardSchemaAlias](../type-aliases/standardschemaalias.md)
- [StorageApi](../type-aliases/storageapi.md)
- [StorageEventApi](../type-aliases/storageeventapi.md)
- [TransactionState](../type-aliases/transactionstate.md)
- [TransactionWithMutations](../type-aliases/transactionwithmutations.md)
- [UpdateMutationFn](../type-aliases/updatemutationfn.md)
- [UpdateMutationFnParams](../type-aliases/updatemutationfnparams.md)
- [UtilsRecord](../type-aliases/utilsrecord.md)

## Variables

- [IndexOperation](../variables/indexoperation.md)
- [Query](../variables/query.md)

## Functions

- [add](../functions/add.md)
- [and](../functions/and.md)
- [avg](../functions/avg.md)
- [coalesce](../functions/coalesce.md)
- [compileQuery](../functions/compilequery.md)
- [concat](../functions/concat.md)
- [count](../functions/count.md)
- [createArrayChangeProxy](../functions/createarraychangeproxy.md)
- [createChangeProxy](../functions/createchangeproxy.md)
- [createCollection](../functions/createcollection.md)
- [createLiveQueryCollection](../functions/createlivequerycollection.md)
- [createOptimisticAction](../functions/createoptimisticaction.md)
- [createTransaction](../functions/createtransaction.md)
- [eq](../functions/eq.md)
- [getActiveTransaction](../functions/getactivetransaction.md)
- [gt](../functions/gt.md)
- [gte](../functions/gte.md)
- [ilike](../functions/ilike.md)
- [inArray](../functions/inarray.md)
- [length](../functions/length.md)
- [like](../functions/like.md)
- [liveQueryCollectionOptions](../functions/livequerycollectionoptions.md)
- [localOnlyCollectionOptions](../functions/localonlycollectionoptions.md)
- [localStorageCollectionOptions](../functions/localstoragecollectionoptions.md)
- [lower](../functions/lower.md)
- [lt](../functions/lt.md)
- [lte](../functions/lte.md)
- [max](../functions/max.md)
- [min](../functions/min.md)
- [not](../functions/not.md)
- [or](../functions/or.md)
- [sum](../functions/sum.md)
- [upper](../functions/upper.md)
- [withArrayChangeTracking](../functions/witharraychangetracking.md)
- [withChangeTracking](../functions/withchangetracking.md)
