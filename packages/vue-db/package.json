{"name": "@tanstack/vue-db", "description": "Vue integration for @tanstack/db", "version": "0.0.39", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/db.git", "directory": "packages/vue-db"}, "homepage": "https://tanstack.com/db", "keywords": ["optimistic", "vue", "typescript"], "packageManager": "pnpm@10.6.3", "dependencies": {"@tanstack/db": "workspace:*"}, "devDependencies": {"@electric-sql/client": "1.0.0", "@vitejs/plugin-vue": "^5.2.4", "@vitest/coverage-istanbul": "^3.0.9", "vue": "^3.5.13"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "peerDependencies": {"vue": ">=3.3.0"}, "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "npx vitest --run", "lint": "eslint . --fix"}, "sideEffects": false, "type": "module", "types": "dist/esm/index.d.ts"}