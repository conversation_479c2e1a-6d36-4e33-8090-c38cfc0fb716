{"name": "@tanstack/db-ivm", "description": "Incremental View Maintenance for TanStack DB based on Differential Dataflow", "version": "0.1.2", "dependencies": {"fractional-indexing": "^3.2.0", "murmurhash-js": "^1.0.0", "sorted-btree": "^1.8.1"}, "devDependencies": {"@types/debug": "^4.1.12", "@types/murmurhash-js": "^1.0.6", "@vitest/coverage-istanbul": "^3.0.9"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "packageManager": "pnpm@10.6.3", "peerDependencies": {"typescript": ">=4.7"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/db.git", "directory": "packages/db-ivm"}, "homepage": "https://tanstack.com/db", "keywords": ["electric", "sql", "optimistic", "typescript", "ivm", "differential-dataflow"], "scripts": {"build": "vite build", "dev": "vite build --watch", "lint": "eslint . --fix", "test": "npx vitest --run"}, "sideEffects": false, "type": "module", "types": "dist/esm/index.d.ts"}