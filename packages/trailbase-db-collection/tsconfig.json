{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2024"], "moduleResolution": "<PERSON><PERSON><PERSON>", "declaration": true, "outDir": "dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "paths": {"@tanstack/store": ["../store/src"]}}, "include": ["src", "tests", "vite.config.ts"], "exclude": ["node_modules", "dist"]}