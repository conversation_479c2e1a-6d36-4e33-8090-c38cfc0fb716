{"name": "@tanstack/db", "description": "A reactive client store for building super fast apps on sync", "version": "0.1.6", "dependencies": {"@standard-schema/spec": "^1.0.0", "@tanstack/db-ivm": "workspace:*"}, "devDependencies": {"@vitest/coverage-istanbul": "^3.0.9", "arktype": "^2.1.20"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "packageManager": "pnpm@10.6.3", "peerDependencies": {"typescript": ">=4.7"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/db.git", "directory": "packages/db"}, "homepage": "https://tanstack.com/db", "keywords": ["optimistic", "typescript"], "scripts": {"build": "vite build", "dev": "vite build --watch", "lint": "eslint . --fix", "test": "npx vitest --run"}, "sideEffects": false, "type": "module", "types": "dist/esm/index.d.ts"}