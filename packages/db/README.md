# @tanstack/db

**A reactive client store for building super fast apps on sync**

TanStack DB extends TanStack Query with collections, live queries and optimistic mutations that keep your UI reactive, consistent and blazing fast 🔥

<p>
  <a href="https://x.com/intent/post?text=TanStack%20DB&url=https://tanstack.com/db">
    <img alt="#TanStack" src="https://img.shields.io/twitter/url?color=%2308a0e9&label=%23TanStack&style=social&url=https%3A%2F%2Ftwitter.com%2Fintent%2Ftweet%3Fbutton_hashtag%3DTanStack" /></a>
  <a href="#status">
    <img src="https://img.shields.io/badge/status-alpha-orange" alt="Status - Alpha"></a>
  <a href="https://npmjs.com/package/@tanstack/db">
    <img alt="" src="https://img.shields.io/npm/dm/@tanstack/db.svg" /></a>
  <a href="https://discord.gg/yjUNbvbraC">
    <img alt="" src="https://img.shields.io/badge/Discord-TanStack-%235865F2" /></a>
  <a href="https://github.com/tanstack/db/discussions">
    <img alt="Join the discussion on Github" src="https://img.shields.io/badge/Discussions-Chat%20now!-green" /></a>
  <a href="https://x.com/tan_stack">
    <img alt="" src="https://img.shields.io/twitter/follow/tan_stack.svg?style=social&label=Follow @TanStack" /></a>
</p>

## 💥 Visit the [TanStack/db](https://github.com/TanStack/db) repo for docs and details!

- 🔥 **blazing fast query engine**<br />
  for sub-millisecond live queries &mdash; even for complex queries with joins and aggregates
- 🎯 **fine-grained reactivity**<br />
  to minimize component re-rendering
- 💪 **robust transaction primitives**<br />
  for easy optimistic mutations with sync and lifecycle support
- 🌟 **normalized data**<br />
  to keep your backend simple

---

### [Become a Sponsor!](https://github.com/sponsors/tannerlinsley/)

Enjoy this library? Try the entire [TanStack](https://tanstack.com), including [TanStack Query](https://tanstack.com/query), [TanStack Store](https://tanstack.com/store), etc.
