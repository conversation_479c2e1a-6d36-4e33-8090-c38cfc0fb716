{"name": "@tanstack/react-db", "description": "React integration for @tanstack/db", "version": "0.1.6", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/db.git", "directory": "packages/react-db"}, "homepage": "https://tanstack.com/db", "keywords": ["optimistic", "react", "typescript"], "packageManager": "pnpm@10.6.3", "dependencies": {"@tanstack/db": "workspace:*", "use-sync-external-store": "^1.2.0"}, "devDependencies": {"@electric-sql/client": "1.0.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.3", "@types/use-sync-external-store": "^0.0.6", "@vitest/coverage-istanbul": "^3.0.9", "react": "^19.0.0", "react-dom": "^19.0.0"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "peerDependencies": {"react": ">=16.8.0"}, "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "npx vitest --run", "lint": "eslint . --fix"}, "sideEffects": false, "type": "module", "types": "dist/esm/index.d.ts"}