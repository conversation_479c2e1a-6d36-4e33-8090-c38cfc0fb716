{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "declaration": true, "outDir": "dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "paths": {"@tanstack/store": ["../store/src"], "@tanstack/db": ["../db/src"], "@tanstack/db-ivm": ["../db-ivm/src"]}}, "include": ["src/**/*", "tests", "vite.config.ts"], "exclude": ["node_modules", "dist"]}