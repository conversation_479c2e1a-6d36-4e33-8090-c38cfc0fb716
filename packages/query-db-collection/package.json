{"name": "@tanstack/query-db-collection", "description": "TanStack Query collection for TanStack DB", "version": "0.2.5", "dependencies": {"@tanstack/db": "workspace:*", "@standard-schema/spec": "^1.0.0"}, "devDependencies": {"@tanstack/query-core": "^5.0.5", "@vitest/coverage-istanbul": "^3.0.9"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "packageManager": "pnpm@10.6.3", "peerDependencies": {"@tanstack/query-core": "^5.0.0", "typescript": ">=4.7"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/db.git", "directory": "packages/query-db-collection"}, "homepage": "https://tanstack.com/db", "keywords": ["query", "tanstack", "optimistic", "typescript"], "scripts": {"build": "vite build", "dev": "vite build --watch", "lint": "eslint . --fix", "test": "npx vitest --run"}, "sideEffects": false, "type": "module", "types": "dist/esm/index.d.ts"}