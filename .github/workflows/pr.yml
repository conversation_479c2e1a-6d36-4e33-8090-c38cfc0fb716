name: PR

on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

permissions:
  contents: read
  pull-requests: write

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0
      - name: Setup Too<PERSON>
        uses: tanstack/config/.github/setup@main
      - name: Get base and head commits for `nx affected`
        uses: nrwl/nx-set-shas@v4.3.0
        with:
          main-branch-name: main
      - name: Run Checks
        run: pnpm run lint && pnpm run build && pnpm run test
  preview:
    name: Preview
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0
      - name: Setup Tools
        uses: tanstack/config/.github/setup@main
      - name: Build Packages
        run: pnpm run build
      - name: Publish Previews
        run: pnpx pkg-pr-new publish --pnpm --compact './packages/*' --template './examples/*/*'
      - name: Compressed Size Action - DB Package
        uses: preactjs/compressed-size-action@v2
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          pattern: "./packages/db/dist/**/*.{js,mjs}"
          comment-key: "db-package-size"
      - name: Compressed Size Action - React DB Package
        uses: preactjs/compressed-size-action@v2
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          pattern: "./packages/react-db/dist/**/*.{js,mjs}"
          comment-key: "react-db-package-size"
  build-example:
    name: Build Example Site
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
      - name: Setup Tools
        uses: tanstack/config/.github/setup@main
      - name: Build Packages
        run: pnpm run build
      - name: Build Example Site
        run: |
          cd examples/react/todo
          pnpm build
  build-starter:
    name: Build Example Site
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
      - name: Setup Tools
        uses: tanstack/config/.github/setup@main
      - name: Build Packages
        run: pnpm run build
      - name: Build Starter Site
        run: |
          cd examples/react/projects
          pnpm build
