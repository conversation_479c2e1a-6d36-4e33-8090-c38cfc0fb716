name: Release

on:
  push:
    branches: [main, alpha, beta]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

env:
  NX_CLOUD_ACCESS_TOKEN: ${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

permissions:
  contents: write
  id-token: write
  pull-requests: write

jobs:
  release:
    name: Release
    if: github.repository_owner == 'TanStack'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 0
      - name: Setup Tools
        uses: tanstack/config/.github/setup@main
      - name: Run Tests
        run: pnpm run lint && pnpm run build && pnpm run test
      - name: Run Changesets (version or publish)
        uses: changesets/action@v1.4.9
        with:
          version: pnpm run changeset:version
          publish: pnpm run changeset:publish
          commit: "ci: Version Packages"
          title: "ci: Version Packages"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
