name: autofix.ci # needed to securely identify the workflow

on:
  pull_request:
  push:
    branches: [main, alpha, beta]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  autofix:
    name: autofix
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.2
      - name: Setup Tools
        uses: tanstack/config/.github/setup@main
      - name: Fix formatting
        run: pnpm prettier --ignore-unknown . --check
      - name: Apply fixes
        uses: autofix-ci/action@551dded8c6cc8a1054039c8bc0b8b48c51dfc6ef
        with:
          commit-message: "ci: apply automated fixes"
