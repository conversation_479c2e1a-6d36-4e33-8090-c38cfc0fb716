packages:
  - "packages/**"
  - "examples/**"
catalog:
  "@changesets/cli": ^2.28.1
  "@electric-sql/client": 1.0.0
  "@electric-sql/d2mini": ^0.1.8
  "@eslint/js": ^9.22.0
  "@solid-primitives/map": ^0.7.2
  "@solidjs/testing-library": ^0.8.10
  "@standard-schema/spec": ^1.0.0
  "@stylistic/eslint-plugin": ^4.2.0
  "@sveltejs/package": ^2.4.0
  "@sveltejs/vite-plugin-svelte": ^6.1.0
  "@svitejs/changesets-changelog-github-compact": ^1.2.0
  "@tailwindcss/vite": ^4.0.0-alpha.8
  "@tanstack/config": ^0.20.0
  "@tanstack/electric-db-collection": ^0.0.9
  "@tanstack/query-core": ^5.75.7
  "@tanstack/query-db-collection": ^0.0.9
  "@tanstack/react-db": ^0.0.33
  "@tanstack/react-router": ^1.125.6
  "@tanstack/react-start": ^1.126.1
  "@tanstack/solid-db": ^0.0.27
  "@tanstack/solid-router": ^1.129.8
  "@tanstack/solid-start": ^1.126.1
  "@tanstack/store": ^0.7.0
  "@tanstack/trailbase-db-collection": ^0.0.3
  "@testing-library/jest-dom": ^6.6.3
  "@testing-library/react": ^16.2.0
  "@types/cors": ^2.8.17
  "@types/debug": ^4.1.12
  "@types/express": ^4.17.21
  "@types/node": ^22.13.10
  "@types/pg": ^8.11.11
  "@types/react": ^19.0.12
  "@types/react-dom": ^19.0.4
  "@types/use-sync-external-store": ^0.0.6
  "@typescript-eslint/eslint-plugin": ^8.26.1
  "@typescript-eslint/parser": ^8.26.1
  "@vitejs/plugin-react": ^4.3.4
  "@vitejs/plugin-vue": ^5.2.4
  "@vitest/coverage-istanbul": ^3.0.9
  arktype: ^2.1.20
  concurrently: ^9.2.0
  cors: ^2.8.5
  debug: ^4.4.1
  dotenv: ^16.3.1
  drizzle-kit: ^0.30.5
  drizzle-orm: ^0.40.1
  drizzle-zod: ^0.7.0
  eslint: ^9.22.0
  eslint-config-prettier: ^10.1.1
  eslint-plugin-prettier: ^5.2.3
  eslint-plugin-react: ^7.37.4
  eslint-plugin-react-hooks: ^5.2.0
  eslint-plugin-react-refresh: ^0.4.5
  eslint-plugin-solid: ^0.14.5
  express: ^4.19.2
  fast-glob: ^3.3.3
  husky: ^9.1.7
  jsdom: ^26.0.0
  lint-staged: ^15.5.0
  markdown-link-extractor: ^4.0.2
  mitt: ^3.0.1
  pg: ^8.14.1
  postgres: ^3.4.7
  prettier: ^3.5.3
  publint: ^0.3.9
  react: ^19.1.0
  react-dom: ^19.1.0
  shx: ^0.4.0
  solid-js: ^1.9.0
  svelte: ^5.28.6
  svelte-check: ^4.3.0
  tailwindcss: ^4.1.11
  trailbase: ^0.7.1
  tsup: ^8.0.2
  tsx: ^4.6.2
  typescript: ^5.8.2
  use-sync-external-store: ^1.2.0
  vite: ^6.2.2
  vite-plugin-solid: ^2.11.7
  vite-tsconfig-paths: ^5.1.4
  vitest: ^3.0.9
  vue: ^3.5.13
  zod: ^3.24.2
catalogs:
  conflicts_@types/react-dom_h19_0_3:
    "@types/react-dom": ^19.0.3
  conflicts_react_h19_0_0:
    react: ^19.0.0
  conflicts_react-dom_h19_0_0:
    react-dom: ^19.0.0
  conflicts_solid-js_h1_9_7:
    solid-js: ^1.9.7
  conflicts_publint_h0_3_2:
    publint: ^0.3.2
