/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/solid-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as TrailbaseRouteImport } from './routes/trailbase'
import { Route as QueryRouteImport } from './routes/query'
import { Route as ElectricRouteImport } from './routes/electric'
import { Route as IndexRouteImport } from './routes/index'
import { ServerRoute as ApiTodosServerRouteImport } from './routes/api/todos'
import { ServerRoute as ApiConfigServerRouteImport } from './routes/api/config'
import { ServerRoute as ApiTodosIdServerRouteImport } from './routes/api/todos.$id'
import { ServerRoute as ApiConfigIdServerRouteImport } from './routes/api/config.$id'

const rootServerRouteImport = createServerRootRoute()

const TrailbaseRoute = TrailbaseRouteImport.update({
  id: '/trailbase',
  path: '/trailbase',
  getParentRoute: () => rootRouteImport,
} as any)
const QueryRoute = QueryRouteImport.update({
  id: '/query',
  path: '/query',
  getParentRoute: () => rootRouteImport,
} as any)
const ElectricRoute = ElectricRouteImport.update({
  id: '/electric',
  path: '/electric',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiTodosServerRoute = ApiTodosServerRouteImport.update({
  id: '/api/todos',
  path: '/api/todos',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiConfigServerRoute = ApiConfigServerRouteImport.update({
  id: '/api/config',
  path: '/api/config',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiTodosIdServerRoute = ApiTodosIdServerRouteImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => ApiTodosServerRoute,
} as any)
const ApiConfigIdServerRoute = ApiConfigIdServerRouteImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => ApiConfigServerRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/electric': typeof ElectricRoute
  '/query': typeof QueryRoute
  '/trailbase': typeof TrailbaseRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/electric': typeof ElectricRoute
  '/query': typeof QueryRoute
  '/trailbase': typeof TrailbaseRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/electric': typeof ElectricRoute
  '/query': typeof QueryRoute
  '/trailbase': typeof TrailbaseRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/electric' | '/query' | '/trailbase'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/electric' | '/query' | '/trailbase'
  id: '__root__' | '/' | '/electric' | '/query' | '/trailbase'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ElectricRoute: typeof ElectricRoute
  QueryRoute: typeof QueryRoute
  TrailbaseRoute: typeof TrailbaseRoute
}
export interface FileServerRoutesByFullPath {
  '/api/config': typeof ApiConfigServerRouteWithChildren
  '/api/todos': typeof ApiTodosServerRouteWithChildren
  '/api/config/$id': typeof ApiConfigIdServerRoute
  '/api/todos/$id': typeof ApiTodosIdServerRoute
}
export interface FileServerRoutesByTo {
  '/api/config': typeof ApiConfigServerRouteWithChildren
  '/api/todos': typeof ApiTodosServerRouteWithChildren
  '/api/config/$id': typeof ApiConfigIdServerRoute
  '/api/todos/$id': typeof ApiTodosIdServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/config': typeof ApiConfigServerRouteWithChildren
  '/api/todos': typeof ApiTodosServerRouteWithChildren
  '/api/config/$id': typeof ApiConfigIdServerRoute
  '/api/todos/$id': typeof ApiTodosIdServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/config' | '/api/todos' | '/api/config/$id' | '/api/todos/$id'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/config' | '/api/todos' | '/api/config/$id' | '/api/todos/$id'
  id:
    | '__root__'
    | '/api/config'
    | '/api/todos'
    | '/api/config/$id'
    | '/api/todos/$id'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiConfigServerRoute: typeof ApiConfigServerRouteWithChildren
  ApiTodosServerRoute: typeof ApiTodosServerRouteWithChildren
}

declare module '@tanstack/solid-router' {
  interface FileRoutesByPath {
    '/trailbase': {
      id: '/trailbase'
      path: '/trailbase'
      fullPath: '/trailbase'
      preLoaderRoute: typeof TrailbaseRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/query': {
      id: '/query'
      path: '/query'
      fullPath: '/query'
      preLoaderRoute: typeof QueryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/electric': {
      id: '/electric'
      path: '/electric'
      fullPath: '/electric'
      preLoaderRoute: typeof ElectricRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}
declare module '@tanstack/solid-start/server' {
  interface ServerFileRoutesByPath {
    '/api/todos': {
      id: '/api/todos'
      path: '/api/todos'
      fullPath: '/api/todos'
      preLoaderRoute: typeof ApiTodosServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/config': {
      id: '/api/config'
      path: '/api/config'
      fullPath: '/api/config'
      preLoaderRoute: typeof ApiConfigServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/todos/$id': {
      id: '/api/todos/$id'
      path: '/$id'
      fullPath: '/api/todos/$id'
      preLoaderRoute: typeof ApiTodosIdServerRouteImport
      parentRoute: typeof ApiTodosServerRoute
    }
    '/api/config/$id': {
      id: '/api/config/$id'
      path: '/$id'
      fullPath: '/api/config/$id'
      preLoaderRoute: typeof ApiConfigIdServerRouteImport
      parentRoute: typeof ApiConfigServerRoute
    }
  }
}

interface ApiConfigServerRouteChildren {
  ApiConfigIdServerRoute: typeof ApiConfigIdServerRoute
}

const ApiConfigServerRouteChildren: ApiConfigServerRouteChildren = {
  ApiConfigIdServerRoute: ApiConfigIdServerRoute,
}

const ApiConfigServerRouteWithChildren = ApiConfigServerRoute._addFileChildren(
  ApiConfigServerRouteChildren,
)

interface ApiTodosServerRouteChildren {
  ApiTodosIdServerRoute: typeof ApiTodosIdServerRoute
}

const ApiTodosServerRouteChildren: ApiTodosServerRouteChildren = {
  ApiTodosIdServerRoute: ApiTodosIdServerRoute,
}

const ApiTodosServerRouteWithChildren = ApiTodosServerRoute._addFileChildren(
  ApiTodosServerRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ElectricRoute: ElectricRoute,
  QueryRoute: QueryRoute,
  TrailbaseRoute: TrailbaseRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiConfigServerRoute: ApiConfigServerRouteWithChildren,
  ApiTodosServerRoute: ApiTodosServerRouteWithChildren,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
