{"name": "@tanstack/db-example-solid-todo", "private": true, "version": "0.0.33", "dependencies": {"@tanstack/electric-db-collection": "^0.0.9", "@tanstack/query-core": "^5.75.7", "@tanstack/query-db-collection": "^0.0.9", "@tanstack/solid-db": "^0.0.27", "@tanstack/solid-router": "^1.129.8", "@tanstack/solid-start": "^1.126.1", "@tanstack/trailbase-db-collection": "^0.0.3", "cors": "^2.8.5", "drizzle-orm": "^0.40.1", "drizzle-zod": "^0.7.0", "express": "^4.19.2", "postgres": "^3.4.7", "solid-js": "^1.9.0", "tailwindcss": "^4.1.11", "trailbase": "^0.7.1", "vite-tsconfig-paths": "^5.1.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.0.0-alpha.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.16.5", "@types/pg": "^8.11.11", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "concurrently": "^9.2.0", "dotenv": "^16.3.1", "drizzle-kit": "^0.30.5", "eslint": "^9.22.0", "eslint-plugin-solid": "^0.14.5", "pg": "^8.14.1", "tsx": "^4.6.2", "typescript": "^5.8.2", "vite": "^6.2.2", "vite-plugin-solid": "^2.11.7"}, "scripts": {"build": "vite build", "db:ensure-config": "tsx scripts/ensure-default-config.ts", "db:generate": "drizzle-kit generate", "db:push": "tsx scripts/migrate.ts", "db:studio": "drizzle-kit studio", "dev": "docker compose up -d && vite dev", "lint": "eslint . --fix", "preview": "vite preview"}, "type": "module"}