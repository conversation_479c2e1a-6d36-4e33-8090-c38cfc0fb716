{"id": "1649b703-2b2d-413c-8185-15390a9d97e7", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.todos": {"name": "todos", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}