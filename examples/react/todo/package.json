{"name": "@tanstack/db-example-react-todo", "private": true, "version": "0.1.1", "dependencies": {"@tanstack/electric-db-collection": "^0.1.0", "@tanstack/query-core": "^5.75.7", "@tanstack/query-db-collection": "^0.2.0", "@tanstack/react-db": "^0.1.0", "@tanstack/react-query-persist-client": "^5.85.5", "@tanstack/react-router": "^1.125.6", "@tanstack/react-start": "^1.126.1", "@tanstack/trailbase-db-collection": "^0.1.0", "cors": "^2.8.5", "drizzle-orm": "^0.40.1", "drizzle-zod": "^0.8.3", "express": "^4.19.2", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "trailbase": "^0.7.1", "vite-tsconfig-paths": "^5.1.4", "zod": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.0.0-alpha.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.13.10", "@types/pg": "^8.11.11", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "dotenv": "^16.3.1", "drizzle-kit": "^0.30.5", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.5", "pg": "^8.14.1", "tsx": "^4.6.2", "typescript": "^5.8.2", "vite": "^6.2.2"}, "scripts": {"build": "vite build", "db:ensure-config": "tsx scripts/ensure-default-config.ts", "db:generate": "drizzle-kit generate", "db:push": "tsx scripts/migrate.ts", "db:studio": "drizzle-kit studio", "dev": "docker compose up -d && vite dev", "lint": "eslint . --fix", "preview": "vite preview"}, "type": "module"}