# Todo example app

## How to run

- Go to the root of the repository and run:
  - `pnpm install`
  - `pnpm build`

- Install packages
  `pnpm install`

- Start dev server & Docker containers: Postgres, Electric, TrailBase
  `pnpm dev`

- Run Postgres DB migrations
  `pnpm db:push`

- Optionally, check out the TrailBase admin UI @ http://localhost:4000/\_/admin
  (email: admin@localhost, password: secret)
