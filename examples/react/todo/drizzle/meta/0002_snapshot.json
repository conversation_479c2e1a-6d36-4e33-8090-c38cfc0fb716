{"id": "017c3a1c-9d70-4fed-ad07-9a2e1ab49bb4", "prevId": "1b3a815a-1865-451b-b646-983e995ee97e", "version": "7", "dialect": "postgresql", "tables": {"public.config": {"name": "config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"config_key_unique": {"name": "config_key_unique", "columns": ["key"], "nullsNotDistinct": false}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.todos": {"name": "todos", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "views": {}, "sequences": {}, "roles": {}, "policies": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}