import { createCollection } from "@tanstack/react-db"
import { queryCollectionOptions } from "@tanstack/query-db-collection"
import { QueryClient } from "@tanstack/query-core"
// Persistence imports removed due to missing dependencies
// import { persistQueryClient } from "@tanstack/react-query-persist-client"
// import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister"
import { selectConfigSchema, selectTodoSchema } from "../db/validation"
import { api } from "./api"
// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache data indefinitely (or set a reasonable staleTime)
      staleTime: Infinity,
      // Retry failed requests when online
      retry: (failureCount, _error) => {
        if (navigator.onLine) {
          return failureCount < 3 // Retry up to 3 times if online
        }
        return false // No retries if offline
      },
    },
  },
})

// Persistence functionality commented out due to missing dependencies
// const persister = createSyncStoragePersister({
//   storage: window.localStorage, // Use localStorage or IndexedDB
//   key: `tanstack-query-cache`,
// })

// persistQueryClient({
//   queryClient,
//   persister,
//   maxAge: 1000 * 60 * 60 * 24, // Cache for 24 hours
// })

// Queue for offline mutations (disabled due to SSR compatibility)
const mutationQueue = {
  queue: [] as any[],
  save: function () {
    if (typeof window !== "undefined" && window.localStorage) {
      localStorage.setItem(`mutationQueue`, JSON.stringify(this.queue))
    }
  },
  add: function (mutation) {
    this.queue.push(mutation)
    this.save()
  },
  clear: function () {
    this.queue = []
    this.save()
  },
}

// Sync queued mutations when online (disabled due to SSR compatibility)
async function syncMutations() {
  if (typeof navigator === "undefined" || !navigator.onLine) return

  const queue = [...mutationQueue.queue]
  for (const mutation of queue) {
    try {
      if (mutation.type === `insert`) {
        await (api as any)[mutation.collection].create(mutation.data)
      } else if (mutation.type === `update`) {
        await (api as any)[mutation.collection].update(
          mutation.id,
          mutation.data
        )
      } else if (mutation.type === `delete`) {
        await (api as any)[mutation.collection].delete(mutation.id)
      }
      // Remove successful mutation from queue
      mutationQueue.queue = mutationQueue.queue.filter(
        (m: any) => m !== mutation
      )
      mutationQueue.save()
    } catch (error) {
      console.error(`Failed to sync mutation:`, error)
    }
  }
}

// Listen for online status to sync mutations (only in browser)
if (typeof window !== "undefined") {
  window.addEventListener(`online`, syncMutations)
}

// Query Todo Collection
export const queryTodoCollection = createCollection(
  queryCollectionOptions({
    id: `todos`,
    queryKey: [`todos`],
    refetchInterval:
      typeof navigator !== "undefined" && navigator.onLine ? 3000 : false, // Disable refetching when offline
    queryFn: async () => {
      try {
        const todos = await api.todos.getAll()
        return todos.map((todo) => ({
          ...todo,
          created_at: new Date(todo.created_at),
          updated_at: new Date(todo.updated_at),
        }))
      } catch (error) {
        if (typeof navigator !== "undefined" && !navigator.onLine) {
          // Return cached data when offline
          const cached = queryClient.getQueryData([`todos`])
          if (cached) return cached
          throw new Error(`No cached data available`)
        }
        throw error
      }
    },
    getKey: (item) => item.id,
    schema: selectTodoSchema,
    queryClient,
    onInsert: async ({ transaction }) => {
      const {
        id: _id,
        created_at: _crea,
        updated_at: _up,
        ...modified
      } = transaction.mutations[0].modified

      // Optimistic update
      queryClient.setQueryData([`todos`], (old = []) => [
        ...old,
        {
          id: Date.now(),
          ...modified,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ])

      // Queue mutation if offline
      if (typeof navigator !== "undefined" && !navigator.onLine) {
        mutationQueue.add({
          type: `insert`,
          collection: `todos`,
          data: modified,
        })
        return { txid: `queued` }
      }

      // Perform mutation if online
      const response = await api.todos.create(modified)
      queryClient.invalidateQueries([`todos`])
      return response
    },
    onUpdate: async ({ transaction }) => {
      const results = await Promise.all(
        transaction.mutations.map(async (mutation) => {
          const { original, changes } = mutation
          if (!(`id` in original)) {
            throw new Error(`Original todo not found for update`)
          }

          // Optimistic update
          queryClient.setQueryData([`todos`], (old = []) =>
            old.map((item) =>
              item.id === original.id ? { ...item, ...changes } : item
            )
          )

          // Queue mutation if offline
          if (typeof navigator !== "undefined" && !navigator.onLine) {
            mutationQueue.add({
              type: `update`,
              collection: `todos`,
              id: original.id,
              data: changes,
            })
            return { txid: `queued` }
          }

          // Perform mutation if online
          return await api.todos.update(original.id, changes)
        })
      )
      queryClient.invalidateQueries([`todos`])
      return results
    },
    onDelete: async ({ transaction }) => {
      const results = await Promise.all(
        transaction.mutations.map(async (mutation) => {
          const { original } = mutation
          if (!(`id` in original)) {
            throw new Error(`Original todo not found for delete`)
          }

          // Optimistic update
          queryClient.setQueryData([`todos`], (old = []) =>
            old.filter((item) => item.id !== original.id)
          )

          // Queue mutation if offline
          if (typeof navigator !== "undefined" && !navigator.onLine) {
            mutationQueue.add({
              type: `delete`,
              collection: `todos`,
              id: original.id,
            })
            return { txid: `queued` }
          }

          // Perform mutation if online
          return await api.todos.delete(original.id)
        })
      )
      queryClient.invalidateQueries([`todos`])
      return results
    },
  })
)

// Query Config Collection
export const queryConfigCollection = createCollection(
  queryCollectionOptions({
    id: `config`,
    queryKey: [`config`],
    refetchInterval:
      typeof navigator !== "undefined" && navigator.onLine ? 3000 : false, // Disable refetching when offline
    queryFn: async () => {
      try {
        const configs = await api.config.getAll()
        return configs.map((config) => ({
          ...config,
          created_at: new Date(config.created_at),
          updated_at: new Date(config.updated_at),
        }))
      } catch (error) {
        if (typeof navigator !== "undefined" && !navigator.onLine) {
          // Return cached data when offline
          const cached = queryClient.getQueryData([`config`])
          if (cached) return cached
          throw new Error(`No cached data available`)
        }
        throw error
      }
    },
    getKey: (item) => item.id,
    schema: selectConfigSchema,
    queryClient,
    onInsert: async ({ transaction }) => {
      const modified = transaction.mutations[0].modified

      // Optimistic update
      queryClient.setQueryData([`config`], (old = []) => [
        ...old,
        {
          id: Date.now(),
          ...modified,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ])

      // Queue mutation if offline
      if (typeof navigator !== "undefined" && !navigator.onLine) {
        mutationQueue.add({
          type: `insert`,
          collection: `config`,
          data: modified,
        })
        return { txid: `queued` }
      }

      // Perform mutation if online
      const response = await api.config.create(modified)
      queryClient.invalidateQueries([`config`])
      return response
    },
    onUpdate: async ({ transaction }) => {
      const results = await Promise.all(
        transaction.mutations.map(async (mutation) => {
          const { original, changes } = mutation
          if (!(`id` in original)) {
            throw new Error(`Original config not found for update`)
          }

          // Optimistic update
          queryClient.setQueryData([`config`], (old = []) =>
            old.map((item) =>
              item.id === original.id ? { ...item, ...changes } : item
            )
          )

          // Queue mutation if offline
          if (typeof navigator !== "undefined" && !navigator.onLine) {
            mutationQueue.add({
              type: `update`,
              collection: `config`,
              id: original.id,
              data: changes,
            })
            return { txid: `queued` }
          }

          // Perform mutation if online
          const response = await api.config.update(original.id, changes)
          return response
        })
      )
      queryClient.invalidateQueries([`config`])
      return results
    },
  })
)
