{"name": "tanstack-start-db-projects-example", "private": true, "type": "module", "scripts": {"dev": "docker compose up -d && vite dev", "start": "node .output/server/index.mjs", "build": "vite build", "migrate": "drizzle-kit migrate", "serve": "vite preview", "test": "vitest run", "lint:check": "eslint .", "lint": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@tailwindcss/vite": "^4.0.6", "@tanstack/query-core": "^5.62.6", "@tanstack/query-db-collection": "^0.0.15", "@tanstack/react-db": "^0.0.33", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.130.2", "@tanstack/react-router-with-query": "^1.130.2", "@tanstack/react-start": "^1.130.3", "@tanstack/router-plugin": "^1.130.2", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "better-auth": "^1.3.4", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.7.1", "pg": "^8.16.3", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwindcss": "^4.0.6", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.25.76"}, "devDependencies": {"@eslint/compat": "^1.3.1", "@eslint/js": "^9.32.0", "@testing-library/dom": "^10.4.1", "@testing-library/react": "^16.2.0", "@types/pg": "^8.15.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "concurrently": "^9.2.0", "drizzle-kit": "^0.31.4", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "jsdom": "^26.0.0", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^5.0.3"}}