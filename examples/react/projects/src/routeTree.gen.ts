/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated/index'
import { Route as AuthenticatedProjectProjectIdRouteImport } from './routes/_authenticated/project/$projectId'
import { ServerRoute as ApiAuthServerRouteImport } from './routes/api/auth'
import { ServerRoute as ApiTrpcSplatServerRouteImport } from './routes/api/trpc/$'

const rootServerRouteImport = createServerRootRoute()

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedProjectProjectIdRoute =
  AuthenticatedProjectProjectIdRouteImport.update({
    id: '/project/$projectId',
    path: '/project/$projectId',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const ApiAuthServerRoute = ApiAuthServerRouteImport.update({
  id: '/api/auth',
  path: '/api/auth',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiTrpcSplatServerRoute = ApiTrpcSplatServerRouteImport.update({
  id: '/api/trpc/$',
  path: '/api/trpc/$',
  getParentRoute: () => rootServerRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof LoginRoute
  '/': typeof AuthenticatedIndexRoute
  '/project/$projectId': typeof AuthenticatedProjectProjectIdRoute
}
export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/': typeof AuthenticatedIndexRoute
  '/project/$projectId': typeof AuthenticatedProjectProjectIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/project/$projectId': typeof AuthenticatedProjectProjectIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/login' | '/' | '/project/$projectId'
  fileRoutesByTo: FileRoutesByTo
  to: '/login' | '/' | '/project/$projectId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/'
    | '/_authenticated/project/$projectId'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}
export interface FileServerRoutesByFullPath {
  '/api/auth': typeof ApiAuthServerRoute
  '/api/trpc/$': typeof ApiTrpcSplatServerRoute
}
export interface FileServerRoutesByTo {
  '/api/auth': typeof ApiAuthServerRoute
  '/api/trpc/$': typeof ApiTrpcSplatServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/auth': typeof ApiAuthServerRoute
  '/api/trpc/$': typeof ApiTrpcSplatServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/auth' | '/api/trpc/$'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/auth' | '/api/trpc/$'
  id: '__root__' | '/api/auth' | '/api/trpc/$'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiAuthServerRoute: typeof ApiAuthServerRoute
  ApiTrpcSplatServerRoute: typeof ApiTrpcSplatServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/project/$projectId': {
      id: '/_authenticated/project/$projectId'
      path: '/project/$projectId'
      fullPath: '/project/$projectId'
      preLoaderRoute: typeof AuthenticatedProjectProjectIdRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/auth': {
      id: '/api/auth'
      path: '/api/auth'
      fullPath: '/api/auth'
      preLoaderRoute: typeof ApiAuthServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/trpc/$': {
      id: '/api/trpc/$'
      path: '/api/trpc/$'
      fullPath: '/api/trpc/$'
      preLoaderRoute: typeof ApiTrpcSplatServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedProjectProjectIdRoute: typeof AuthenticatedProjectProjectIdRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedProjectProjectIdRoute: AuthenticatedProjectProjectIdRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiAuthServerRoute: ApiAuthServerRoute,
  ApiTrpcSplatServerRoute: ApiTrpcSplatServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
