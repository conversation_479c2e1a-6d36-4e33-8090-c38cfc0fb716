{"name": "@tanstack/db-monorepo", "private": true, "description": "Reactive client queryable store for sync-first applications", "version": "0.0.0", "repository": {"type": "git", "url": "https://github.com/tanstack/db.git"}, "author": "<PERSON> <<EMAIL>>", "packageManager": "pnpm@10.6.3", "type": "module", "keywords": [], "license": "MIT", "scripts": {"build": "pnpm --filter \"./packages/**\" build", "changeset": "changeset", "changeset:publish": "changeset publish", "changeset:version": "changeset version && pnpm install --no-frozen-lockfile", "docs:generate": "node scripts/generateDocs.js", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "lint": "pnpm --filter \"./packages/**\" lint", "lint-all": "eslint . --fix", "prepare": "husky", "test": "pnpm --filter \"./packages/**\" test", "test:verify-links": "node scripts/verify-links.ts"}, "devDependencies": {"@changesets/cli": "^2.28.1", "@eslint/js": "^9.22.0", "@stylistic/eslint-plugin": "^4.2.0", "@svitejs/changesets-changelog-github-compact": "^1.2.0", "@tanstack/config": "^0.20.0", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^22.13.10", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/use-sync-external-store": "^0.0.6", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "husky": "^9.1.7", "fast-glob": "^3.3.3", "jsdom": "^26.0.0", "markdown-link-extractor": "^4.0.2", "lint-staged": "^15.5.0", "mitt": "^3.0.1", "prettier": "^3.5.3", "publint": "^0.3.9", "shx": "^0.4.0", "tsup": "^8.0.2", "typescript": "^5.8.2", "vite": "^6.2.2", "vitest": "^3.0.9", "zod": "^3.24.2"}, "exports": {"./package.json": "./package.json"}, "files": ["dist", "src"], "lint-staged": {"*.{ts,tsx}": ["eslint --fix"]}, "workspaces": ["packages/*", "examples/*", "examples/react/*"], "overrides": {"@tanstack/db": "workspace:*", "@tanstack/db-ivm": "workspace:*", "@tanstack/react-db": "workspace:*", "@tanstack/vue-db": "workspace:*"}}