{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "checkJs": true,
    "declaration": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "module": "ES2022",
    "moduleResolution": "Bundler",
    "noEmit": true,
    "noImplicitReturns": true,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": false, // Let ESLint handle this
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "ES2020",
    "noErrorTruncation": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "test/**/*.ts",
    "vite.config.ts",
    "vitest.config.ts",
    "todo-app/**/*.ts",
    "todo-app/**/*.tsx",
    "examples/**/*.ts",
    "examples/**/*.tsx",
    "eslint.config.mjs",
    "scripts/verify-links.ts"
  ],
  "exclude": ["node_modules"]
}
